<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.TablesMapper">
	<select id="findTablesList"  resultType="Tables">
		select * from tables 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Tables">
	    select  *  
        from tables a  	
		<where>
      		<if test="tid != null and tid !=0 ">
		    and a.tid = #{tid}
		</if>
		<if test="pid != null and pid !=0 ">
		    and a.pid = #{pid}
		</if>
		<if test="tword != null and tword != ''">
		    and a.tword = #{tword}
		</if>
		<if test="tname != null and tname != ''">
		    and a.tname = #{tname}
		</if>
		<if test="tgn != null and tgn != ''">
		    and a.tgn = #{tgn}
		</if>
		<if test="tlist != null and tlist != ''">
		    and a.tlist = #{tlist}
		</if>
		<if test="vlist != null and vlist != ''">
		    and a.vlist = #{vlist}
		</if>
		<if test="by1 != null and by1 != ''">
		    and a.by1 = #{by1}
		</if>
		<if test="by2 != null and by2 != ''">
		    and a.by2 = #{by2}
		</if>
		<if test="by3 != null and by3 != ''">
		    and a.by3 = #{by3}
		</if>
		<if test="by4 != null and by4 != ''">
		    and a.by4 = #{by4}
		</if>
		<if test="by5 != null and by5 != ''">
		    and a.by5 = #{by5}
		</if>
		<if test="by6 != null and by6 != ''">
		    and a.by6 = #{by6}
		</if>
		<if test="by9 != null and by9 != ''">
		    and a.by9 = #{by9}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} tid 

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from tables a  
		<where>
      		<if test="tid != null and tid !=0 ">
		    and a.tid = #{tid}
		</if>
		<if test="pid != null and pid !=0 ">
		    and a.pid = #{pid}
		</if>
		<if test="tword != null and tword != ''">
		    and a.tword = #{tword}
		</if>
		<if test="tname != null and tname != ''">
		    and a.tname = #{tname}
		</if>
		<if test="tgn != null and tgn != ''">
		    and a.tgn = #{tgn}
		</if>
		<if test="tlist != null and tlist != ''">
		    and a.tlist = #{tlist}
		</if>
		<if test="vlist != null and vlist != ''">
		    and a.vlist = #{vlist}
		</if>
		<if test="by1 != null and by1 != ''">
		    and a.by1 = #{by1}
		</if>
		<if test="by2 != null and by2 != ''">
		    and a.by2 = #{by2}
		</if>
		<if test="by3 != null and by3 != ''">
		    and a.by3 = #{by3}
		</if>
		<if test="by4 != null and by4 != ''">
		    and a.by4 = #{by4}
		</if>
		<if test="by5 != null and by5 != ''">
		    and a.by5 = #{by5}
		</if>
		<if test="by6 != null and by6 != ''">
		    and a.by6 = #{by6}
		</if>
		<if test="by9 != null and by9 != ''">
		    and a.by9 = #{by9}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryTablesById" parameterType="int" resultType="Tables">
    select  *  
     from tables a  	 where a.tid=#{value}
  </select>
 
	<insert id="insertTables" useGeneratedKeys="true" keyProperty="tid" parameterType="Tables">
    insert into tables
    (pid,tword,tname,tgn,tlist,vlist,by1,by2,by3,by4,by5,by6,by7,by8,by9,by10)
    values
    (#{pid},#{tword},#{tname},#{tgn},#{tlist},#{vlist},#{by1},#{by2},#{by3},#{by4},#{by5},#{by6},#{by7},#{by8},#{by9},#{by10});
  </insert>
	
	<update id="updateTables" parameterType="Tables" >
    update tables 
    <set>
		<if test="pid != null ">
		    pid = #{pid},
		</if>
		<if test="tword != null and tword != ''">
		    tword = #{tword},
		</if>
		<if test="tname != null and tname != ''">
		    tname = #{tname},
		</if>
		<if test="tgn != null and tgn != ''">
		    tgn = #{tgn},
		</if>
		<if test="tlist != null and tlist != ''">
		    tlist = #{tlist},
		</if>
		<if test="vlist != null and vlist != ''">
		    vlist = #{vlist},
		</if>
		<if test="by1 != null and by1 != ''">
		    by1 = #{by1},
		</if>
		<if test="by2 != null and by2 != ''">
		    by2 = #{by2},
		</if>
		<if test="by3 != null and by3 != ''">
		    by3 = #{by3},
		</if>
		<if test="by4 != null and by4 != ''">
		    by4 = #{by4},
		</if>
		<if test="by5 != null and by5 != ''">
		    by5 = #{by5},
		</if>
		<if test="by6 != null and by6 != ''">
		    by6 = #{by6},
		</if>
		<if test="by7 != null and by7 != ''">
		    by7 = #{by7},
		</if>
		<if test="by8 != null and by8 != ''">
		    by8 = #{by8},
		</if>
		<if test="by9 != null and by9 != ''">
		    by9 = #{by9},
		</if>
		<if test="by10 != null and by10 != ''">
		    by10 = #{by10},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="tid != null or tid != ''">
      tid=#{tid}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteTables" parameterType="int">
    delete from  tables where tid=#{value}
  </delete>

	
	
</mapper>

 
