<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
       <el-form :model="formData" label-width="20%" align="left">
<el-form-item label="小类ID">
{{formData.sid}}</el-form-item>
<el-form-item label="大类">
{{formData.bname}}</el-form-item>
<el-form-item label="小类名称">
{{formData.sname}}</el-form-item>
<el-form-item label="内容1">
{{formData.memo1}}</el-form-item>
<el-form-item label="内容2">
{{formData.memo2}}</el-form-item>
<el-form-item label="内容3">
{{formData.memo3}}</el-form-item>
<el-form-item label="内容4" prop="memo4">
<div v-html="formData.memo4"></div>
</el-form-item>
<el-form-item label="备用1">
{{formData.by1}}</el-form-item>
<el-form-item label="备用2">
{{formData.by2}}</el-form-item>
<el-form-item label="备用3">
{{formData.by3}}</el-form-item>
<el-form-item label="备用4">
{{formData.by4}}</el-form-item>
<el-form-item label="备用5">
{{formData.by5}}</el-form-item>
<el-form-item label="备用6">
{{formData.by6}}</el-form-item>
<el-form-item label="备用7">
{{formData.by7}}</el-form-item>
<el-form-item label="备用8">
{{formData.by8}}</el-form-item>
<el-form-item label="备用9">
{{formData.by9}}</el-form-item>
<el-form-item label="备用10">
{{formData.by10}}</el-form-item>
<el-form-item>
<el-button type="info" size="small" @click="back" icon="el-icon-back">返 回</el-button>
</el-form-item>
</el-form>


    </div>
</template>

<script>
        
        import request, { base } from "../../../../utils/http";
        export default {
            name: 'SmallDetail',
            components: {
            },
            data() {
                return {
                    id: '',
                    formData: {}, //表单数据         
        
                };
            },
            created() {
                this.id = this.$route.query.id; //获取参数
                this.getDatas();
            },
        
        
            methods: {
        
                //获取列表数据
                getDatas() {
                    let para = {
                    };
                    this.listLoading = true;
                    let url = base + "/small/get?id=" + this.id;
                    request.post(url, para).then((res) => {
                        this.formData = JSON.parse(JSON.stringify(res.resdata));
                        this.listLoading = false;
                    });
                },
        
                // 返回
                back() {
                    //返回上一页
                    this.$router.go(-1);
                },
        
            },
        }

</script>
<style scoped>
</style>
 

