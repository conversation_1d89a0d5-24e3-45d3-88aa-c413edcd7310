# Spring Boot Application Configuration
# 应用程序配置文件

# ==================== 服务器配置 ====================
# 服务器端口
server.port=${serverPort}
# 应用程序上下文路径
server.servlet.context-path=/

# ==================== 数据源配置 ====================
# 数据库连接配置 - ${databaseTypeComment}
spring.datasource.driver-class-name=${driverClassName}
spring.datasource.url=${databaseUrl}
spring.datasource.username=${databaseUsername}
spring.datasource.password=${databasePassword}

# 连接池配置
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# ==================== MyBatis配置 ====================
# MyBatis配置文件位置
mybatis.config-location=classpath:mybatis/sqlMapConfig.xml
# Mapper XML文件位置
mybatis.mapper-locations=classpath:com/mapper/*.xml
# 实体类包路径
mybatis.type-aliases-package=com.model
# 开启驼峰命名转换
mybatis.configuration.map-underscore-to-camel-case=true
# 开启二级缓存
mybatis.configuration.cache-enabled=true
# 延迟加载
mybatis.configuration.lazy-loading-enabled=true
mybatis.configuration.aggressive-lazy-loading=false

# ==================== 日志配置 ====================
# 日志级别
logging.level.root=INFO
logging.level.com.mapper=DEBUG
logging.level.org.springframework=INFO
# 日志输出格式
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# ==================== Thymeleaf配置 ====================
# 模板缓存（开发时设为false）
spring.thymeleaf.cache=false
# 模板编码
spring.thymeleaf.encoding=UTF-8
# 模板模式
spring.thymeleaf.mode=HTML
# 模板前缀
spring.thymeleaf.prefix=classpath:/templates/
# 模板后缀
spring.thymeleaf.suffix=.html

# ==================== 文件上传配置 ====================
# 单个文件最大大小
spring.servlet.multipart.max-file-size=10MB
# 总上传大小限制
spring.servlet.multipart.max-request-size=50MB
# 文件上传临时目录
spring.servlet.multipart.location=/tmp

# ==================== 静态资源配置 ====================
# 静态资源路径
spring.web.resources.static-locations=classpath:/static/
# 静态资源缓存时间（秒）
spring.web.resources.cache.period=3600

# ==================== 开发工具配置 ====================
# 开启热部署
spring.devtools.restart.enabled=true
# 热部署监控目录
spring.devtools.restart.additional-paths=src/main/java
# 排除监控目录
spring.devtools.restart.exclude=static/**,templates/**

# ==================== 应用信息配置 ====================
# 应用名称
spring.application.name=${projectName}
# 应用描述
info.app.name=${projectChineseName}
info.app.description=基于Spring Boot和MyBatis的Web应用程序
info.app.version=1.0.0
info.app.encoding=UTF-8
info.java.version=${java.version}
