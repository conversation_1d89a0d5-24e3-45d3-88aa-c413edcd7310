<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.MoresMapper">
	<select id="findMoresList"  resultType="Mores">
		select * from mores 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Mores">
	    select  *  
        from mores a  	
		<where>
      		<if test="mid != null and mid !=0 ">
		    and a.mid = #{mid}
		</if>
		<if test="tid != null and tid !=0 ">
		    and a.tid = #{tid}
		</if>
		<if test="moname != null and moname != ''">
		    and a.moname = #{moname}
		</if>
		<if test="mozname != null and mozname != ''">
		    and a.mozname = #{mozname}
		</if>
		<if test="motype != null and motype != ''">
		    and a.motype = #{motype}
		</if>
		<if test="moflag != null and moflag != ''">
		    and a.moflag = #{moflag}
		</if>
		<if test="molong != null and molong != ''">
		    and a.molong = #{molong}
		</if>
		<if test="moyz != null and moyz != ''">
		    and a.moyz = #{moyz}
		</if>
		<if test="mobt != null and mobt != ''">
		    and a.mobt = #{mobt}
		</if>
		<if test="by1 != null and by1 != ''">
		    and a.by1 = #{by1}
		</if>
		<if test="by2 != null and by2 != ''">
		    and a.by2 = #{by2}
		</if>
		<if test="by3 != null and by3 != ''">
		    and a.by3 = #{by3}
		</if>
		<if test="by4 != null and by4 != ''">
		    and a.by4 = #{by4}
		</if>
		<if test="by5 != null and by5 != ''">
		    and a.by5 = #{by5}
		</if>
		<if test="by6 != null and by6 != ''">
		    and a.by6 = #{by6}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} mid 

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from mores a  
		<where>
      		<if test="mid != null and mid !=0 ">
		    and a.mid = #{mid}
		</if>
		<if test="tid != null and tid !=0 ">
		    and a.tid = #{tid}
		</if>
		<if test="moname != null and moname != ''">
		    and a.moname = #{moname}
		</if>
		<if test="mozname != null and mozname != ''">
		    and a.mozname = #{mozname}
		</if>
		<if test="motype != null and motype != ''">
		    and a.motype = #{motype}
		</if>
		<if test="moflag != null and moflag != ''">
		    and a.moflag = #{moflag}
		</if>
		<if test="molong != null and molong != ''">
		    and a.molong = #{molong}
		</if>
		<if test="moyz != null and moyz != ''">
		    and a.moyz = #{moyz}
		</if>
		<if test="mobt != null and mobt != ''">
		    and a.mobt = #{mobt}
		</if>
		<if test="by1 != null and by1 != ''">
		    and a.by1 = #{by1}
		</if>
		<if test="by2 != null and by2 != ''">
		    and a.by2 = #{by2}
		</if>
		<if test="by3 != null and by3 != ''">
		    and a.by3 = #{by3}
		</if>
		<if test="by4 != null and by4 != ''">
		    and a.by4 = #{by4}
		</if>
		<if test="by5 != null and by5 != ''">
		    and a.by5 = #{by5}
		</if>
		<if test="by6 != null and by6 != ''">
		    and a.by6 = #{by6}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryMoresById" parameterType="int" resultType="Mores">
    select  *  
     from mores a  	 where a.mid=#{value}
  </select>
 
	<insert id="insertMores" useGeneratedKeys="true" keyProperty="mid" parameterType="Mores">
    insert into mores
    (tid,moname,mozname,motype,moflag,molong,moyz,mobt,by1,by2,by3,by4,by5,by6)
    values
    (#{tid},#{moname},#{mozname},#{motype},#{moflag},#{molong},#{moyz},#{mobt},#{by1},#{by2},#{by3},#{by4},#{by5},#{by6});
  </insert>
	
	<update id="updateMores" parameterType="Mores" >
    update mores 
    <set>
		<if test="tid != null ">
		    tid = #{tid},
		</if>
		<if test="moname != null and moname != ''">
		    moname = #{moname},
		</if>
		<if test="mozname != null and mozname != ''">
		    mozname = #{mozname},
		</if>
		<if test="motype != null and motype != ''">
		    motype = #{motype},
		</if>
		<if test="moflag != null and moflag != ''">
		    moflag = #{moflag},
		</if>
		<if test="molong != null and molong != ''">
		    molong = #{molong},
		</if>
		<if test="moyz != null and moyz != ''">
		    moyz = #{moyz},
		</if>
		<if test="mobt != null and mobt != ''">
		    mobt = #{mobt},
		</if>
		<if test="by1 != null and by1 != ''">
		    by1 = #{by1},
		</if>
		<if test="by2 != null and by2 != ''">
		    by2 = #{by2},
		</if>
		<if test="by3 != null and by3 != ''">
		    by3 = #{by3},
		</if>
		<if test="by4 != null and by4 != ''">
		    by4 = #{by4},
		</if>
		<if test="by5 != null and by5 != ''">
		    by5 = #{by5},
		</if>
		<if test="by6 != null and by6 != ''">
		    by6 = #{by6},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="mid != null or mid != ''">
      mid=#{mid}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteMores" parameterType="int">
    delete from  mores where mid=#{value}
  </delete>

	<delete id="deleteMoresByTid" parameterType="int">
    delete from  mores where tid=#{value}
  </delete>



</mapper>

 
