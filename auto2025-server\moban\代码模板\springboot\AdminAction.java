package ${packageName}.controller;

import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import com.response.Response;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import ${packageName}.model.*;
import ${packageName}.service.*;
import ${packageName}.util.*;

/**
 * ${entityComment}控制器类
 *
 * 功能说明：
 * 1. 处理${entityComment}相关的HTTP请求
 * 2. 提供完整的CRUD操作接口
 * 3. 支持分页查询和条件查询
 * 4. 返回对应的视图页面或JSON数据
 */
@Controller
public class ${entityName}Action{

	@Autowired private ${entityName}Service ${entityNameLower}Service;

	// 查询所有${entityComment}
	@RequestMapping(value = "/${entityNameLower}List")
	public String ${entityNameLower}List(${entityName} ser,HttpServletRequest req)throws Exception
	{

		int offset = 0; // 记录偏移量
		int counts = 0; // 总记录数
		try {
			offset = Integer.parseInt(req.getParameter("pager.offset"));
		} catch (Exception e) {
		}
		int pageSize = 10; // 每页显示条记录数
		PageBean page = new PageBean(offset); // 实例化分页类
		page.setPageSize(pageSize); // 设置每页显示记录数

		counts = ${entityNameLower}Service.getCount(ser);
		List<${entityName}> ${entityNameLower}List = ${entityNameLower}Service.query${entityName}List(ser, page); // 查询所有${entityComment}
${editorFieldsProcessing}
		req.setAttribute("list", ${entityNameLower}List);

		/** 分页代码开始 **/
		req.setAttribute("itemSize", counts); // 总记录数
		int page_count = (counts + pageSize - 1) / pageSize; // 计算总页数
		req.setAttribute("pageItem", pageSize); // 每页记录数
		req.setAttribute("pageTotal", page_count); // 总页数
		req.setAttribute("currentPageNumber", (offset / pageSize) + 1); // 当前页码
		req.setAttribute("pageUrl", "${entityNameLower}List"); // 跳转路径
		/** 分页代码 结束 **/
		return "/admin/${entityNameLower}/${entityNameLower}_Manage";
	}

	// 查询所有${entityComment}（列表页面）
	@RequestMapping(value = "/${entityNameLower}List2")
	public String ${entityNameLower}List2(${entityName} ser,HttpServletRequest req)throws Exception
	{

		int offset = 0; // 记录偏移量
		int counts = 0; // 总记录数
		try {
			offset = Integer.parseInt(req.getParameter("pager.offset"));
		} catch (Exception e) {
		}
		int pageSize = 10; // 每页显示条记录数
		PageBean page = new PageBean(offset); // 实例化分页类
		page.setPageSize(pageSize); // 设置每页显示记录数

		counts = ${entityNameLower}Service.getCount(ser);
		List<${entityName}> ${entityNameLower}List = ${entityNameLower}Service.query${entityName}List(ser, page); // 查询所有${entityComment}
${editorFieldsProcessing}
		req.setAttribute("list", ${entityNameLower}List);

		/** 分页代码开始 **/
		req.setAttribute("itemSize", counts); // 总记录数
		int page_count = (counts + pageSize - 1) / pageSize; // 计算总页数
		req.setAttribute("pageItem", pageSize); // 每页记录数
		req.setAttribute("pageTotal", page_count); // 总页数
		req.setAttribute("currentPageNumber", (offset / pageSize) + 1); // 当前页码
		req.setAttribute("pageUrl", "${entityNameLower}List2"); // 跳转路径
		/** 分页代码 结束 **/
		return "/admin/${entityNameLower}/${entityNameLower}_Manage2";
	}

	// 跳转到${entityComment}添加页面
	@RequestMapping(value = "/${entityNameLower}ToAdd")
	public String ${entityNameLower}ToAdd(HttpServletRequest req) throws Exception {

		return "/admin/${entityNameLower}/${entityNameLower}_Add";
	}

	// 添加${entityComment}
	@RequestMapping(value = "/${entityNameLower}Add")
	@ResponseBody
	public Response ${entityNameLower}Add(${entityName} ${entityNameLower}, HttpServletRequest req) throws Exception {
${existsCheckCode}
		return Response.success();
	}

	// 删除${entityComment}
	@RequestMapping(value = "/${entityNameLower}Del")
	public String ${entityNameLower}Del(HttpServletRequest req) throws Exception {
${idParamCode}
		${entityNameLower}Service.delete${entityName}(id); // 删除${entityComment}
		req.setAttribute("message", "操作成功");
		req.setAttribute("path", "${entityNameLower}List");
		return "common/succeed";
	}

	// 跳转到${entityComment}修改页面
	@RequestMapping(value = "/${entityNameLower}ToEdit")
	public String ${entityNameLower}ToEdit(HttpServletRequest req) throws Exception {
${idParamCode}
		${entityName} ${entityNameLower} = ${entityNameLower}Service.query${entityName}ById(id); // 根据ID查询${entityComment}详情
		req.setAttribute("item", ${entityNameLower});

		return "/admin/${entityNameLower}/${entityNameLower}_Edit";
	}

	// 跳转到${entityComment}详情页面
	@RequestMapping(value = "/${entityNameLower}ToDetail")
	public String ${entityNameLower}ToDetail(HttpServletRequest req) throws Exception {
${idParamCode}
		${entityName} ${entityNameLower} = ${entityNameLower}Service.query${entityName}ById(id); // 根据ID查询${entityComment}详情
		req.setAttribute("item", ${entityNameLower}); // 设置${entityComment}对象到请求属性中
		return "/admin/${entityNameLower}/${entityNameLower}_Detail";
	}

	// 修改${entityComment}
	@RequestMapping(value = "/${entityNameLower}Edit")
	@ResponseBody
	public Response ${entityNameLower}Edit(${entityName} ${entityNameLower}, HttpServletRequest req) throws Exception {
		${entityNameLower}Service.update${entityName}(${entityNameLower}); // 更新${entityComment}
		return Response.success();
	}
${loginMethod}
${passwordMethod}
${registrationAndProfileMethods}
}
