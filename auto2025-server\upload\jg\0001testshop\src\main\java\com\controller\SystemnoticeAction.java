package com.controller;

import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import com.response.Response;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.model.*;
import com.service.*;
import com.util.*;

/**
 * 系统公告控制器类
 *
 * 功能说明：
 * 1. 处理系统公告相关的HTTP请求
 * 2. 提供完整的CRUD操作接口
 * 3. 支持分页查询和条件查询
 * 4. 返回对应的视图页面或JSON数据
 */
@Controller
public class SystemnoticeAction{

	@Autowired private SystemnoticeService systemnoticeService;

	// 查询所有系统公告
	@RequestMapping(value = "/systemnoticeList")
	public String systemnoticeList(Systemnotice ser,HttpServletRequest req)throws Exception
	{

		int offset = 0; // 记录偏移量
		int counts = 0; // 总记录数
		try {
			offset = Integer.parseInt(req.getParameter("pager.offset"));
		} catch (Exception e) {
		}
		int pageSize = 10; // 每页显示条记录数
		PageBean page = new PageBean(offset); // 实例化分页类
		page.setPageSize(pageSize); // 设置每页显示记录数

		counts = systemnoticeService.getCount(ser);
		List<Systemnotice> systemnoticeList = systemnoticeService.querySystemnoticeList(ser, page); // 查询所有系统公告

		//遍历
		for (Systemnotice systemnotice : systemnoticeList) {
			systemnotice.setContent(removeHTML.Html2Text(systemnotice.getContent()));
		}

		req.setAttribute("list", systemnoticeList);

		/** 分页代码开始 **/
		req.setAttribute("itemSize", counts); // 总记录数
		int page_count = (counts + pageSize - 1) / pageSize; // 计算总页数
		req.setAttribute("pageItem", pageSize); // 每页记录数
		req.setAttribute("pageTotal", page_count); // 总页数
		req.setAttribute("currentPageNumber", (offset / pageSize) + 1); // 当前页码
		req.setAttribute("pageUrl", "systemnoticeList"); // 跳转路径
		/** 分页代码 结束 **/
		return "/admin/systemnotice/systemnotice_Manage";
	}

	// 查询所有系统公告（列表页面）
	@RequestMapping(value = "/systemnoticeList2")
	public String systemnoticeList2(Systemnotice ser,HttpServletRequest req)throws Exception
	{

		int offset = 0; // 记录偏移量
		int counts = 0; // 总记录数
		try {
			offset = Integer.parseInt(req.getParameter("pager.offset"));
		} catch (Exception e) {
		}
		int pageSize = 10; // 每页显示条记录数
		PageBean page = new PageBean(offset); // 实例化分页类
		page.setPageSize(pageSize); // 设置每页显示记录数

		counts = systemnoticeService.getCount(ser);
		List<Systemnotice> systemnoticeList = systemnoticeService.querySystemnoticeList(ser, page); // 查询所有系统公告

		//遍历
		for (Systemnotice systemnotice : systemnoticeList) {
			systemnotice.setContent(removeHTML.Html2Text(systemnotice.getContent()));
		}

		req.setAttribute("list", systemnoticeList);

		/** 分页代码开始 **/
		req.setAttribute("itemSize", counts); // 总记录数
		int page_count = (counts + pageSize - 1) / pageSize; // 计算总页数
		req.setAttribute("pageItem", pageSize); // 每页记录数
		req.setAttribute("pageTotal", page_count); // 总页数
		req.setAttribute("currentPageNumber", (offset / pageSize) + 1); // 当前页码
		req.setAttribute("pageUrl", "systemnoticeList2"); // 跳转路径
		/** 分页代码 结束 **/
		return "/admin/systemnotice/systemnotice_Manage2";
	}

	// 跳转到系统公告添加页面
	@RequestMapping(value = "/systemnoticeToAdd")
	public String systemnoticeToAdd(HttpServletRequest req) throws Exception {

		return "/admin/systemnotice/systemnotice_Add";
	}

	// 添加系统公告
	@RequestMapping(value = "/systemnoticeAdd")
	@ResponseBody
	public Response systemnoticeAdd(Systemnotice systemnotice, HttpServletRequest req) throws Exception {
		systemnoticeService.insertSystemnotice(systemnotice); // 添加系统公告

		return Response.success();
	}

	// 删除系统公告
	@RequestMapping(value = "/systemnoticeDel")
	public String systemnoticeDel(HttpServletRequest req) throws Exception {
		int id = Integer.parseInt(req.getParameter("id"));
		systemnoticeService.deleteSystemnotice(id); // 删除系统公告
		req.setAttribute("message", "操作成功");
		req.setAttribute("path", "systemnoticeList");
		return "common/succeed";
	}

	// 跳转到系统公告修改页面
	@RequestMapping(value = "/systemnoticeToEdit")
	public String systemnoticeToEdit(HttpServletRequest req) throws Exception {
		int id = Integer.parseInt(req.getParameter("id"));
		Systemnotice systemnotice = systemnoticeService.querySystemnoticeById(id); // 根据ID查询系统公告详情
		req.setAttribute("item", systemnotice);

		return "/admin/systemnotice/systemnotice_Edit";
	}

	// 跳转到系统公告详情页面
	@RequestMapping(value = "/systemnoticeToDetail")
	public String systemnoticeToDetail(HttpServletRequest req) throws Exception {
		int id = Integer.parseInt(req.getParameter("id"));
		Systemnotice systemnotice = systemnoticeService.querySystemnoticeById(id); // 根据ID查询系统公告详情
		req.setAttribute("item", systemnotice); // 设置系统公告对象到请求属性中
		return "/admin/systemnotice/systemnotice_Detail";
	}

	// 修改系统公告
	@RequestMapping(value = "/systemnoticeEdit")
	@ResponseBody
	public Response systemnoticeEdit(Systemnotice systemnotice, HttpServletRequest req) throws Exception {
		systemnoticeService.updateSystemnotice(systemnotice); // 更新系统公告
		return Response.success();
	}



}
