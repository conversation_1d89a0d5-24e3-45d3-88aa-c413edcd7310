package com.controller;

import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import com.response.Response;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.model.*;
import com.service.*;
import com.util.*;

/**
 * 管理员控制器类
 *
 * 功能说明：
 * 1. 处理管理员相关的HTTP请求
 * 2. 提供完整的CRUD操作接口
 * 3. 支持分页查询和条件查询
 * 4. 返回对应的视图页面或JSON数据
 */
@Controller
public class AdminAction{

	@Autowired private AdminService adminService;

	// 查询所有管理员
	@RequestMapping(value = "/adminList")
	public String adminList(Admin ser,HttpServletRequest req)throws Exception
	{

		int offset = 0; // 记录偏移量
		int counts = 0; // 总记录数
		try {
			offset = Integer.parseInt(req.getParameter("pager.offset"));
		} catch (Exception e) {
		}
		int pageSize = 10; // 每页显示条记录数
		PageBean page = new PageBean(offset); // 实例化分页类
		page.setPageSize(pageSize); // 设置每页显示记录数

		counts = adminService.getCount(ser);
		List<Admin> adminList = adminService.queryAdminList(ser, page); // 查询所有管理员

		req.setAttribute("list", adminList);

		/** 分页代码开始 **/
		req.setAttribute("itemSize", counts); // 总记录数
		int page_count = (counts + pageSize - 1) / pageSize; // 计算总页数
		req.setAttribute("pageItem", pageSize); // 每页记录数
		req.setAttribute("pageTotal", page_count); // 总页数
		req.setAttribute("currentPageNumber", (offset / pageSize) + 1); // 当前页码
		req.setAttribute("pageUrl", "adminList"); // 跳转路径
		/** 分页代码 结束 **/
		return "/admin/admin/admin_Manage";
	}

	// 查询所有管理员（列表页面）
	@RequestMapping(value = "/adminList2")
	public String adminList2(Admin ser,HttpServletRequest req)throws Exception
	{

		int offset = 0; // 记录偏移量
		int counts = 0; // 总记录数
		try {
			offset = Integer.parseInt(req.getParameter("pager.offset"));
		} catch (Exception e) {
		}
		int pageSize = 10; // 每页显示条记录数
		PageBean page = new PageBean(offset); // 实例化分页类
		page.setPageSize(pageSize); // 设置每页显示记录数

		counts = adminService.getCount(ser);
		List<Admin> adminList = adminService.queryAdminList(ser, page); // 查询所有管理员

		req.setAttribute("list", adminList);

		/** 分页代码开始 **/
		req.setAttribute("itemSize", counts); // 总记录数
		int page_count = (counts + pageSize - 1) / pageSize; // 计算总页数
		req.setAttribute("pageItem", pageSize); // 每页记录数
		req.setAttribute("pageTotal", page_count); // 总页数
		req.setAttribute("currentPageNumber", (offset / pageSize) + 1); // 当前页码
		req.setAttribute("pageUrl", "adminList2"); // 跳转路径
		/** 分页代码 结束 **/
		return "/admin/admin/admin_Manage2";
	}

	// 跳转到管理员添加页面
	@RequestMapping(value = "/adminToAdd")
	public String adminToAdd(HttpServletRequest req) throws Exception {

		return "/admin/admin/admin_Add";
	}

	// 添加管理员
	@RequestMapping(value = "/adminAdd")
	@ResponseBody
	public Response adminAdd(Admin admin, HttpServletRequest req) throws Exception {
		Admin checkAdmin1 = new Admin();
		checkAdmin1.setLname(admin.getLname());

		List<Admin> checkList1 = adminService.queryAdminList(checkAdmin1, null);
		if (checkList1 != null && checkList1.size() > 0) {
			return Response.error(201, "该账号已存在，请重新输入!");
		} else {
			adminService.insertAdmin(admin); // 添加管理员
		}
		return Response.success();
	}

	// 删除管理员
	@RequestMapping(value = "/adminDel")
	public String adminDel(HttpServletRequest req) throws Exception {
		int id = Integer.parseInt(req.getParameter("id"));
		adminService.deleteAdmin(id); // 删除管理员
		req.setAttribute("message", "操作成功");
		req.setAttribute("path", "adminList");
		return "common/succeed";
	}

	// 跳转到管理员修改页面
	@RequestMapping(value = "/adminToEdit")
	public String adminToEdit(HttpServletRequest req) throws Exception {
		int id = Integer.parseInt(req.getParameter("id"));
		Admin admin = adminService.queryAdminById(id); // 根据ID查询管理员详情
		req.setAttribute("item", admin);

		return "/admin/admin/admin_Edit";
	}

	// 跳转到管理员详情页面
	@RequestMapping(value = "/adminToDetail")
	public String adminToDetail(HttpServletRequest req) throws Exception {
		int id = Integer.parseInt(req.getParameter("id"));
		Admin admin = adminService.queryAdminById(id); // 根据ID查询管理员详情
		req.setAttribute("item", admin); // 设置管理员对象到请求属性中
		return "/admin/admin/admin_Detail";
	}

	// 修改管理员
	@RequestMapping(value = "/adminEdit")
	@ResponseBody
	public Response adminEdit(Admin admin, HttpServletRequest req) throws Exception {
		adminService.updateAdmin(admin); // 更新管理员
		return Response.success();
	}



}
