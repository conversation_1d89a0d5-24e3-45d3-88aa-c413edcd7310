<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.ElogsMapper">
	<select id="findElogsList"  resultType="Elogs">
		select * from elogs 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Elogs">
	    select  *  
        from elogs a  	
		<where>
      		<if test="id != null and id !=0 ">
		    and a.id = #{id}
		</if>
		<if test="pid != null and pid !=0 ">
		    and a.pid = #{pid}
		</if>
		<if test="addtime != null and addtime != ''">
		    and a.addtime = #{addtime}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} id desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from elogs a  
		<where>
      		<if test="id != null and id !=0 ">
		    and a.id = #{id}
		</if>
		<if test="pid != null and pid !=0 ">
		    and a.pid = #{pid}
		</if>
		<if test="addtime != null and addtime != ''">
		    and a.addtime = #{addtime}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryElogsById" parameterType="int" resultType="Elogs">
    select  *  
     from elogs a  	 where a.id=#{value}
  </select>
 
	<insert id="insertElogs" useGeneratedKeys="true" keyProperty="id" parameterType="Elogs">
    insert into elogs
    (pid,memo,addtime)
    values
    (#{pid},#{memo},now());
  </insert>
	
	<update id="updateElogs" parameterType="Elogs" >
    update elogs 
    <set>
		<if test="pid != null ">
		    pid = #{pid},
		</if>
		<if test="memo != null and memo != ''">
		    memo = #{memo},
		</if>
		<if test="addtime != null and addtime != ''">
		    addtime = #{addtime},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="id != null or id != ''">
      id=#{id}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteElogs" parameterType="int">
    delete from  elogs where id=#{value}
  </delete>

	
	
</mapper>

 
