package com.controller;

import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import com.response.Response;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.model.*;
import com.service.*;
import com.util.*;

/**
 * 用户信息控制器类
 *
 * 功能说明：
 * 1. 处理用户信息相关的HTTP请求
 * 2. 提供完整的CRUD操作接口
 * 3. 支持分页查询和条件查询
 * 4. 返回对应的视图页面或JSON数据
 */
@Controller
public class UserinfoAction{

	@Autowired private UserinfoService userinfoService;

	// 查询所有用户信息
	@RequestMapping(value = "/userinfoList")
	public String userinfoList(Userinfo ser,HttpServletRequest req)throws Exception
	{

		int offset = 0; // 记录偏移量
		int counts = 0; // 总记录数
		try {
			offset = Integer.parseInt(req.getParameter("pager.offset"));
		} catch (Exception e) {
		}
		int pageSize = 10; // 每页显示条记录数
		PageBean page = new PageBean(offset); // 实例化分页类
		page.setPageSize(pageSize); // 设置每页显示记录数

		counts = userinfoService.getCount(ser);
		List<Userinfo> userinfoList = userinfoService.queryUserinfoList(ser, page); // 查询所有用户信息

		req.setAttribute("list", userinfoList);

		/** 分页代码开始 **/
		req.setAttribute("itemSize", counts); // 总记录数
		int page_count = (counts + pageSize - 1) / pageSize; // 计算总页数
		req.setAttribute("pageItem", pageSize); // 每页记录数
		req.setAttribute("pageTotal", page_count); // 总页数
		req.setAttribute("currentPageNumber", (offset / pageSize) + 1); // 当前页码
		req.setAttribute("pageUrl", "userinfoList"); // 跳转路径
		/** 分页代码 结束 **/
		return "/admin/userinfo/userinfo_Manage";
	}

	// 查询所有用户信息（列表页面）
	@RequestMapping(value = "/userinfoList2")
	public String userinfoList2(Userinfo ser,HttpServletRequest req)throws Exception
	{

		int offset = 0; // 记录偏移量
		int counts = 0; // 总记录数
		try {
			offset = Integer.parseInt(req.getParameter("pager.offset"));
		} catch (Exception e) {
		}
		int pageSize = 10; // 每页显示条记录数
		PageBean page = new PageBean(offset); // 实例化分页类
		page.setPageSize(pageSize); // 设置每页显示记录数

		counts = userinfoService.getCount(ser);
		List<Userinfo> userinfoList = userinfoService.queryUserinfoList(ser, page); // 查询所有用户信息

		req.setAttribute("list", userinfoList);

		/** 分页代码开始 **/
		req.setAttribute("itemSize", counts); // 总记录数
		int page_count = (counts + pageSize - 1) / pageSize; // 计算总页数
		req.setAttribute("pageItem", pageSize); // 每页记录数
		req.setAttribute("pageTotal", page_count); // 总页数
		req.setAttribute("currentPageNumber", (offset / pageSize) + 1); // 当前页码
		req.setAttribute("pageUrl", "userinfoList2"); // 跳转路径
		/** 分页代码 结束 **/
		return "/admin/userinfo/userinfo_Manage2";
	}

	// 跳转到用户信息添加页面
	@RequestMapping(value = "/userinfoToAdd")
	public String userinfoToAdd(HttpServletRequest req) throws Exception {

		return "/admin/userinfo/userinfo_Add";
	}

	// 添加用户信息
	@RequestMapping(value = "/userinfoAdd")
	@ResponseBody
	public Response userinfoAdd(Userinfo userinfo, HttpServletRequest req) throws Exception {
		Userinfo checkUserinfo1 = new Userinfo();
		checkUserinfo1.setAccount(userinfo.getAccount());

		List<Userinfo> checkList1 = userinfoService.queryUserinfoList(checkUserinfo1, null);
		if (checkList1 != null && checkList1.size() > 0) {
			return Response.error(201, "该用户账号已存在，请重新输入!");
		} else {
		Userinfo checkUserinfo2 = new Userinfo();
		checkUserinfo2.setPhone(userinfo.getPhone());

		List<Userinfo> checkList2 = userinfoService.queryUserinfoList(checkUserinfo2, null);
		if (checkList2 != null && checkList2.size() > 0) {
			return Response.error(201, "该手机号码已存在，请重新输入!");
		} else {
			userinfoService.insertUserinfo(userinfo); // 添加用户信息
		}
		}
		return Response.success();
	}

	// 删除用户信息
	@RequestMapping(value = "/userinfoDel")
	public String userinfoDel(HttpServletRequest req) throws Exception {
		String id = req.getParameter("id");
		userinfoService.deleteUserinfo(id); // 删除用户信息
		req.setAttribute("message", "操作成功");
		req.setAttribute("path", "userinfoList");
		return "common/succeed";
	}

	// 跳转到用户信息修改页面
	@RequestMapping(value = "/userinfoToEdit")
	public String userinfoToEdit(HttpServletRequest req) throws Exception {
		String id = req.getParameter("id");
		Userinfo userinfo = userinfoService.queryUserinfoById(id); // 根据ID查询用户信息详情
		req.setAttribute("item", userinfo);

		return "/admin/userinfo/userinfo_Edit";
	}

	// 跳转到用户信息详情页面
	@RequestMapping(value = "/userinfoToDetail")
	public String userinfoToDetail(HttpServletRequest req) throws Exception {
		String id = req.getParameter("id");
		Userinfo userinfo = userinfoService.queryUserinfoById(id); // 根据ID查询用户信息详情
		req.setAttribute("item", userinfo); // 设置用户信息对象到请求属性中
		return "/admin/userinfo/userinfo_Detail";
	}

	// 修改用户信息
	@RequestMapping(value = "/userinfoEdit")
	@ResponseBody
	public Response userinfoEdit(Userinfo userinfo, HttpServletRequest req) throws Exception {
		userinfoService.updateUserinfo(userinfo); // 更新用户信息
		return Response.success();
	}


	// 跳转到注册页面
	@RequestMapping(value="/touserinfoReg")
	public String touserinfoReg(HttpServletRequest req)throws Exception{
	    
		return "/userinfo_Reg";
	}

	// 用户信息注册
	@RequestMapping(value = "/userinfoReg")
	public String userinfoReg(Userinfo userinfo, HttpServletRequest request) throws Exception {

		Userinfo te = new Userinfo();
		te.setPassword(userinfo.getPassword());
		String pass2 = request.getParameter("pwd2");
		List<Userinfo> list = userinfoService.queryUserinfoList(te, null);
		if (list != null && list.size() > 0) {
			request.setAttribute("alert", "该用户名已存在，请重新输入");
			} else {

			if (pass2.equals(userinfo.getPassword())) {
				userinfoService.insertUserinfo(userinfo);
				request.setAttribute("alert2", "注册成功，请登录");
				request.setAttribute("path", "qtologin");
				} else {
				request.setAttribute("alert", "两次密码输入不一致，请重新输入");
			}
		}
		return "common/succeed";
	}

	// 跳转到修改个人信息页面
	@RequestMapping(value="/touserinfoInfo")
	public String touserinfoInfo(HttpServletRequest req)throws Exception{
		String id = (String)req.getSession().getAttribute("id");
		Userinfo userinfo = userinfoService.queryUserinfoById(id);
		req.setAttribute("item", userinfo);
		
		return "/admin/userinfo/userinfo_Info";
	}

	// 修改个人信息
	@RequestMapping(value="/userinfoInfo")
	public String userinfoInfo(Userinfo userinfo,HttpServletRequest req)throws Exception{
	    			
		userinfoService.updateUserinfo(userinfo);
	    		
		req.setAttribute("message","操作成功");
		req.setAttribute("path","touserinfoInfo");
		return "common/succeed";
	}


}
