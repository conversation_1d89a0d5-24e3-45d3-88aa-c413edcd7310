package com.controller;

import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import com.response.Response;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.model.*;
import com.service.*;
import com.util.*;
@Controller
public class IndexAction {
	
	@Autowired private AdminService adminService;
	@Autowired private UserinfoService userinfoService;
	

	@RequestMapping("/")
	public String index(HttpServletResponse response) {
		return "login"; // 重定向到首页
	}

	// 后台首页
	@RequestMapping(value = "/adminindex")
	public String adminindex(HttpServletRequest request) throws Exception {
		return "admin/index"; // 跳转到后台首页
	}

	// 后台右侧页面
	@RequestMapping(value = "/toright")
	public String toright(HttpServletRequest request) throws Exception {
		return "admin/right"; // 跳转到后台右侧页面
	}

	// 后台修改密码页面
	@RequestMapping(value = "/toadminpass")
	public String toadminpass(HttpServletRequest request) throws Exception {
		return "admin/admin/pass"; // 跳转到后台修改密码页面
	}

	// 退出登录
	@RequestMapping(value = "/toquit")
	public String toquit(HttpServletRequest req) throws Exception {
		HttpSession session = req.getSession();
		session.invalidate(); // 清除session

		req.setAttribute("message", "退出成功");
		req.setAttribute("path", "toAdminLogin");
		return "common/succeed";
	}

	// 登录
	@RequestMapping(value = "/adminLogin", produces = "text/html;charset=UTF-8")
	public String adminLogin(HttpServletRequest req) throws Exception {

		req.setCharacterEncoding("UTF-8");
		String role = req.getParameter("role");

		if (role.equals("管理员")) {
			Admin admin = new Admin();
			admin.setLname(req.getParameter("txtUserName"));
			admin.setPassword(req.getParameter("txtPassWord"));

			List<Admin> adminList = adminService.queryAdminList(admin, null);
			if (adminList != null && adminList.size() > 0) {
				Admin admin2 = (Admin) adminList.get(0);
				HttpSession session = req.getSession();
				session.setAttribute("id", admin2.getId());
				session.setAttribute("lname", admin2.getLname());
				session.setAttribute("role", "管理员");
				req.setAttribute("message", "登录成功");
				req.setAttribute("path", "adminindex"); // 跳转到后台首页
			} else {
				req.setAttribute("alert", "用户名或密码错误");
				req.setAttribute("path", "/login.jsp");
			}
		} else if (role.equals("用户信息")) {
			Userinfo userinfo = new Userinfo();
			userinfo.setAccount(req.getParameter("txtUserName"));
			userinfo.setPassword(req.getParameter("txtPassWord"));

			List<Userinfo> userinfoList = userinfoService.queryUserinfoList(userinfo, null);
			if (userinfoList != null && userinfoList.size() > 0) {
				Userinfo userinfo2 = (Userinfo) userinfoList.get(0);
				HttpSession session = req.getSession();
				session.setAttribute("id", userinfo2.getAccount());
				session.setAttribute("lname", userinfo2.getAccount());
				session.setAttribute("role", "用户信息");
				req.setAttribute("message", "登录成功");
				req.setAttribute("path", "adminindex"); // 跳转到后台首页
			} else {
				req.setAttribute("alert", "用户名或密码错误");
				req.setAttribute("path", "/login.jsp");
			}
		}

		return "common/succeed";
	}

	// 修改密码
	@RequestMapping(value = "/adminPass")
	@ResponseBody
	public Response adminPass(HttpServletRequest req) throws Exception {
		HttpSession session = req.getSession();

		String pwd1 = req.getParameter("txt_pwd");
		String pwd2 = req.getParameter("txt_pwd2");
		String pwd3 = req.getParameter("txt_pwd3");

		if (!pwd2.equals(pwd3)) {
			return Response.error(201, "新密码和确认密码不一致，请重新输入");
		} else {

			String role = (String) session.getAttribute("role");// 用户角色
			if (role.equals("管理员")) {
				Integer Aid = (Integer) session.getAttribute("id");// 用户ID

				Admin admin = new Admin();
				admin.setId(Aid);
				admin.setPassword(pwd1);

				int temp = adminService.getCount(admin); // 查询数据库中是否存在该用户
				if (temp > 0) {
					admin.setPassword(pwd2); // 设置新密码

					adminService.updateAdmin(admin); // 更新密码

				} else {
					return Response.error(201, "原密码错误，请重新输入");

				}
			} else if (role.equals("用户信息")) {
				String Aid = (String) session.getAttribute("id");// 用户ID

				Userinfo userinfo = new Userinfo();
				userinfo.setAccount(Aid);
				userinfo.setPassword(pwd1);

				int temp = userinfoService.getCount(userinfo); // 查询数据库中是否存在该用户
				if (temp > 0) {
					userinfo.setPassword(pwd2); // 设置新密码

					userinfoService.updateUserinfo(userinfo); // 更新密码

				} else {
					return Response.error(201, "原密码错误，请重新输入");

				}
			}

		}
		return Response.success();
	}



}
