<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.BigMapper">
	<select id="findBigList"  resultType="Big">
		select * from big 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Big">
	    select  *  
        from big a  	
		<where>
      		<if test="bid != null and bid !=0 ">
		    and a.bid = #{bid}
		</if>
		<if test="bname != null and bname != ''">
		    and a.bname = #{bname}
		</if>
		<if test="by1 != null and by1 != ''">
		    and a.by1 = #{by1}
		</if>
		<if test="by2 != null and by2 != ''">
		    and a.by2 = #{by2}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} bid desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from big a  
		<where>
      		<if test="bid != null and bid !=0 ">
		    and a.bid = #{bid}
		</if>
		<if test="bname != null and bname != ''">
		    and a.bname = #{bname}
		</if>
		<if test="by1 != null and by1 != ''">
		    and a.by1 = #{by1}
		</if>
		<if test="by2 != null and by2 != ''">
		    and a.by2 = #{by2}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryBigById" parameterType="int" resultType="Big">
    select  *  
     from big a  	 where a.bid=#{value}
  </select>
 
	<insert id="insertBig" useGeneratedKeys="true" keyProperty="bid" parameterType="Big">
    insert into big
    (bname,bmemo,by1,by2)
    values
    (#{bname},#{bmemo},#{by1},#{by2});
  </insert>
	
	<update id="updateBig" parameterType="Big" >
    update big 
    <set>
		<if test="bname != null and bname != ''">
		    bname = #{bname},
		</if>
		<if test="bmemo != null and bmemo != ''">
		    bmemo = #{bmemo},
		</if>
		<if test="by1 != null and by1 != ''">
		    by1 = #{by1},
		</if>
		<if test="by2 != null and by2 != ''">
		    by2 = #{by2},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="bid != null or bid != ''">
      bid=#{bid}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteBig" parameterType="int">
    delete from  big where bid=#{value}
  </delete>

	
	
</mapper>

 
