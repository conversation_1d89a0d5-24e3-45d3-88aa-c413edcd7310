package com.controller;

import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import com.response.Response;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.model.*;
import com.service.*;
import com.util.*;
@Controller
public class IndexAction {
	
$mingcheng$	

	@RequestMapping("/")
	public String index(HttpServletResponse response) {
		return "login"; // 重定向到首页
	}

	// 后台首页
	@RequestMapping(value = "/adminindex")
	public String adminindex(HttpServletRequest request) throws Exception {
		return "admin/index"; // 跳转到后台首页
	}

	// 后台右侧页面
	@RequestMapping(value = "/toright")
	public String toright(HttpServletRequest request) throws Exception {
		return "admin/right"; // 跳转到后台右侧页面
	}

	// 后台修改密码页面
	@RequestMapping(value = "/toadminpass")
	public String toadminpass(HttpServletRequest request) throws Exception {
		return "admin/admin/pass"; // 跳转到后台修改密码页面
	}

	// 退出登录
	@RequestMapping(value = "/toquit")
	public String toquit(HttpServletRequest req) throws Exception {
		HttpSession session = req.getSession();
		session.invalidate(); // 清除session

		req.setAttribute("message", "退出成功");
		req.setAttribute("path", "toAdminLogin");
		return "common/succeed";
	}

$memo$



}
