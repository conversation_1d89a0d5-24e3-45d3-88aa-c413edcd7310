<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站后台管理系统</title>

    <!-- Bootstrap CSS -->
    <link href="/admin/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="/admin/css/bootstrap-icons.css" rel="stylesheet">

    <!-- 自定义样式 -->
    <link href="/admin/css/custom.css" rel="stylesheet">
</head>
<body>
    <div class="login-page">
        <!-- 动态浮动背景元素 -->
        <div class="floating-shapes">
            <div class="floating-shape"></div>
            <div class="floating-shape"></div>
            <div class="floating-shape"></div>
            <div class="floating-shape"></div>
            <div class="floating-shape"></div>
        </div>

        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-5">
                    <div class="login-card fade-in-up">
                        <!-- Logo -->
                        <img src="/admin/image/logo.svg" alt="Logo" class="login-logo">

                        <!-- 标题 -->
                        <h2 class="login-title">网站后台管理系统</h2>
                        <p class="login-subtitle">请登录您的账户</p>

                <form id="loginForm" novalidate action="adminLogin" method="post">
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="bi bi-person-fill me-2"></i>用户名
                                </label>
                                <input type="text" class="form-control" id="txtUserName" name="txtUserName" required>
                                <div class="invalid-feedback">
                                    请输入用户名
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="bi bi-lock-fill me-2"></i>密码
                                </label>
                                <div class="position-relative">
                                    <input type="password" class="form-control" id="txtPassWord" name="txtPassWord" required>
                                    <button type="button" class="btn btn-link position-absolute end-0 top-50 translate-middle-y" id="togglePassword">
                                        <i class="bi bi-eye" id="passwordIcon"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback">
                                    请输入密码
                                </div>
                            </div>
<div class="mb-3">
                            
                                <div class="position-relative">
                                  <input type="radio" name="role" value="管理员" checked="checked" />管理员
<input type="radio" name="role" value="用户信息" />用户信息

                                </div>
                               
                            </div> 
                            


                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-login text-white">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>登录
                                </button>
                            </div>
                        </form>
<script>
        function showRegistration() {
            // 创建Bootstrap Modal
            var modalId = 'registrationModal-' + Date.now();
            var modalHtml =
                '<div class="modal fade" id="' + modalId + '" tabindex="-1" aria-hidden="true">' +
                '<div class="modal-dialog modal-dialog-centered modal-lg">' +
                '<div class="modal-content">' +
                '<div class="modal-header">' +
                '<h5 class="modal-title">' +
                '<i class="bi bi-person-plus me-2"></i>用户注册' +
                '</h5>' +
                '<button type="button" class="btn-close" data-bs-dismiss="modal"></button>' +
                '</div>' +
                '<div class="modal-body p-0">' +
                '<iframe src="/touserinfoReg" style="width: 100%; height: 400px; border: none;"></iframe>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>';

            // 添加modal到页面
            $('body').append(modalHtml);
            var modal = new bootstrap.Modal(document.getElementById(modalId));

            // 显示modal
            modal.show();

            // modal关闭后移除DOM元素
            $('#' + modalId).on('hidden.bs.modal', function () {
                $(this).remove();
            });
        }
    </script>
    <div class="text-center mt-4">
        <a onclick="showRegistration();" class="text-decoration-none">注册</a>
    </div>

                   

                     
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/admin/js/bootstrap.bundle.js">
    // 全局变量
    window.CommonUtils = {
        // Bootstrap Toast提示函数
        showToast: function (message, type, duration) {
            type = type || 'success';
            duration = duration || 3000;

            var toastClass = '';
            var iconClass = '';
            var title = '';

            switch (type) {
                case 'success':
                    toastClass = 'bg-success';
                    iconClass = 'bi bi-check-circle-fill';
                    title = '成功';
                    break;
                case 'error':
                case 'danger':
                    toastClass = 'bg-danger';
                    iconClass = 'bi bi-exclamation-circle-fill';
                    title = '错误';
                    break;
                case 'warning':
                    toastClass = 'bg-warning';
                    iconClass = 'bi bi-exclamation-triangle-fill';
                    title = '警告';
                    break;
                case 'info':
                    toastClass = 'bg-info';
                    iconClass = 'bi bi-info-circle-fill';
                    title = '提示';
                    break;
                default:
                    toastClass = 'bg-primary';
                    iconClass = 'bi bi-info-circle-fill';
                    title = '提示';
            }

            var toastHtml =
                '<div class="toast align-items-center text-white ' + toastClass + ' border-0" role="alert" aria-live="assertive" aria-atomic="true">' +
                '<div class="d-flex">' +
                '<div class="toast-body">' +
                '<i class="' + iconClass + ' me-2"></i>' + message +
                '</div>' +
                '<button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>' +
                '</div>' +
                '</div>';

            var toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                document.body.appendChild(toastContainer);
            }

            toastContainer.innerHTML = toastHtml;
            var toastElement = toastContainer.querySelector('.toast');
            var toast = new bootstrap.Toast(toastElement, { delay: duration });
            toast.show();
        },

        // 确认删除对话框
        confirmDelete: function (message, callback) {
            message = message || '确定要删除这条记录吗？此操作不可恢复！';

            // 创建Bootstrap Modal
            var modalId = 'deleteModal-' + Date.now();
            var modalHtml =
                '<div class="modal fade" id="' + modalId + '" tabindex="-1" aria-hidden="true">' +
                '<div class="modal-dialog modal-dialog-centered">' +
                '<div class="modal-content">' +
                '<div class="modal-header bg-danger text-white">' +
                '<h5 class="modal-title" style="font-weight: normal; font-size: 16px;">' +
                '<i class="bi bi-exclamation-triangle-fill me-2"></i>确认删除' +
                '</h5>' +
                '<button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>' +
                '</div>' +
                '<div class="modal-body">' +
                '<div class="text-center">' +
                '<i class="bi bi-trash3 text-danger" style="font-size: 3rem; margin-bottom: 1rem;"></i>' +
                '<p style="font-size: 14px; font-weight: normal;">' + message + '</p>' +
                '</div>' +
                '</div>' +
                '<div class="modal-footer">' +
                '<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">' +
                '<i class="bi bi-x-lg me-1"></i>取消' +
                '</button>' +
                '<button type="button" class="btn btn-danger" id="confirmDeleteBtn">' +
                '<i class="bi bi-trash3 me-1"></i>确认删除' +
                '</button>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>';

            // 添加modal到页面
            $('body').append(modalHtml);
            var modal = new bootstrap.Modal(document.getElementById(modalId));

            // 绑定确认按钮事件
            $('#' + modalId + ' #confirmDeleteBtn').on('click', function () {
                modal.hide();
                if (typeof callback === 'function') {
                    callback();
                }
            });

            // 显示modal
            modal.show();

            // modal关闭后移除DOM元素
            $('#' + modalId).on('hidden.bs.modal', function () {
                $(this).remove();
            });
        }
    };
</script>

</body>
</html>
