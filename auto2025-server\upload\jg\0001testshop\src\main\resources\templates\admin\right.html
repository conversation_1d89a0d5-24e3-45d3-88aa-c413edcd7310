<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Bootstrap CSS -->
    <link href="/admin/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="/admin/css/bootstrap-icons.css" rel="stylesheet">

    <script src="/admin/js/bootstrap.bundle.js">
    // 全局变量
    window.CommonUtils = {
        // Bootstrap Toast提示函数
        showToast: function (message, type, duration) {
            type = type || 'success';
            duration = duration || 3000;

            var toastClass = '';
            var iconClass = '';
            var title = '';

            switch (type) {
                case 'success':
                    toastClass = 'bg-success';
                    iconClass = 'bi bi-check-circle-fill';
                    title = '成功';
                    break;
                case 'error':
                case 'danger':
                    toastClass = 'bg-danger';
                    iconClass = 'bi bi-exclamation-circle-fill';
                    title = '错误';
                    break;
                case 'warning':
                    toastClass = 'bg-warning';
                    iconClass = 'bi bi-exclamation-triangle-fill';
                    title = '警告';
                    break;
                case 'info':
                    toastClass = 'bg-info';
                    iconClass = 'bi bi-info-circle-fill';
                    title = '提示';
                    break;
                default:
                    toastClass = 'bg-primary';
                    iconClass = 'bi bi-info-circle-fill';
                    title = '提示';
            }

            var toastHtml =
                '<div class="toast align-items-center text-white ' + toastClass + ' border-0" role="alert" aria-live="assertive" aria-atomic="true">' +
                '<div class="d-flex">' +
                '<div class="toast-body">' +
                '<i class="' + iconClass + ' me-2"></i>' + message +
                '</div>' +
                '<button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>' +
                '</div>' +
                '</div>';

            var toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                document.body.appendChild(toastContainer);
            }

            toastContainer.innerHTML = toastHtml;
            var toastElement = toastContainer.querySelector('.toast');
            var toast = new bootstrap.Toast(toastElement, { delay: duration });
            toast.show();
        },

        // 确认删除对话框
        confirmDelete: function (message, callback) {
            message = message || '确定要删除这条记录吗？此操作不可恢复！';

            // 创建Bootstrap Modal
            var modalId = 'deleteModal-' + Date.now();
            var modalHtml =
                '<div class="modal fade" id="' + modalId + '" tabindex="-1" aria-hidden="true">' +
                '<div class="modal-dialog modal-dialog-centered">' +
                '<div class="modal-content">' +
                '<div class="modal-header bg-danger text-white">' +
                '<h5 class="modal-title" style="font-weight: normal; font-size: 16px;">' +
                '<i class="bi bi-exclamation-triangle-fill me-2"></i>确认删除' +
                '</h5>' +
                '<button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>' +
                '</div>' +
                '<div class="modal-body">' +
                '<div class="text-center">' +
                '<i class="bi bi-trash3 text-danger" style="font-size: 3rem; margin-bottom: 1rem;"></i>' +
                '<p style="font-size: 14px; font-weight: normal;">' + message + '</p>' +
                '</div>' +
                '</div>' +
                '<div class="modal-footer">' +
                '<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">' +
                '<i class="bi bi-x-lg me-1"></i>取消' +
                '</button>' +
                '<button type="button" class="btn btn-danger" id="confirmDeleteBtn">' +
                '<i class="bi bi-trash3 me-1"></i>确认删除' +
                '</button>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>';

            // 添加modal到页面
            $('body').append(modalHtml);
            var modal = new bootstrap.Modal(document.getElementById(modalId));

            // 绑定确认按钮事件
            $('#' + modalId + ' #confirmDeleteBtn').on('click', function () {
                modal.hide();
                if (typeof callback === 'function') {
                    callback();
                }
            });

            // 显示modal
            modal.show();

            // modal关闭后移除DOM元素
            $('#' + modalId).on('hidden.bs.modal', function () {
                $(this).remove();
            });
        }
    };
</script>

    <style>
        .dashboard-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            color: white;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            overflow: hidden;
            position: relative;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .dashboard-card:hover::before {
            opacity: 1;
        }

        .dashboard-card.card-1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .dashboard-card.card-2 {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .dashboard-card.card-3 {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .dashboard-card.card-4 {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .card-icon {
            font-size: 3rem;
            opacity: 0.8;
        }

        .card-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin: 15px 0 10px 0;
        }

        .card-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .welcome-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            color: white;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .page-title {
            color: #333;
            font-weight: 600;
            margin-bottom: 30px;
        }

        .content-area {
            background: #f8f9fa;
            min-height: 100vh;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        .animate-card:nth-child(1) {
            animation-delay: 0.1s;
        }

        .animate-card:nth-child(2) {
            animation-delay: 0.2s;
        }

        .animate-card:nth-child(3) {
            animation-delay: 0.3s;
        }

        .animate-card:nth-child(4) {
            animation-delay: 0.4s;
        }
    </style>
</head>

<body>
    <div class="content-area" style="height: 100%; padding: 20px;">
        <h4 class="page-title">后台首页</h4>

        <!-- 欢迎信息 -->
        <div class="welcome-section">
            <i class="bi bi-house-heart" style="font-size: 3rem; margin-bottom: 15px;"></i>
            <h2>欢迎回来！</h2>
            <p class="mb-0">今天是美好的一天，让我们开始工作吧！</p>
        </div>

        <!-- 统计卡片 -->
        <div class="row">
            <!-- 用户总数 -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card dashboard-card card-1 animate-card">
                    <div class="card-body text-center">
                        <i class="bi bi-people-fill card-icon"></i>
                        <div class="card-number">1,248</div>
                        <div class="card-label">用户总数</div>
                    </div>
                </div>
            </div>

            <!-- 今日订单 -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card dashboard-card card-2 animate-card">
                    <div class="card-body text-center">
                        <i class="bi bi-cart-fill card-icon"></i>
                        <div class="card-number">356</div>
                        <div class="card-label">今日订单</div>
                    </div>
                </div>
            </div>

            <!-- 本月销售额 -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card dashboard-card card-3 animate-card">
                    <div class="card-body text-center">
                        <i class="bi bi-currency-yen card-icon"></i>
                        <div class="card-number">¥128,456</div>
                        <div class="card-label">本月销售额</div>
                    </div>
                </div>
            </div>

            <!-- 系统性能 -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card dashboard-card card-4 animate-card">
                    <div class="card-body text-center">
                        <i class="bi bi-speedometer2 card-icon"></i>
                        <div class="card-number">98.5%</div>
                        <div class="card-label">系统性能</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
</body>

</html>