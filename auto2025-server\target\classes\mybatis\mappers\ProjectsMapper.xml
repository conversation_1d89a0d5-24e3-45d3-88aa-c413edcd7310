<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.ProjectsMapper">
	<select id="findProjectsList"  resultType="Projects">
		select * from projects 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Projects">
	    select  *  
        from projects a  	
		<where>
      		<if test="pid != null and pid !=0 ">
		    and a.pid = #{pid}
		</if>
		<if test="ptype != null and ptype != ''">
		    and a.ptype = #{ptype}
		</if>
		<if test="dtype != null and dtype != ''">
		    and a.dtype = #{dtype}
		</if>
		<if test="pflag != null and pflag != ''">
		    and a.pflag = #{pflag}
		</if>
		<if test="pno != null and pno != ''">
		    and a.pno = #{pno}
		</if>
		<if test="daname != null and daname != ''">
		    and a.daname = #{daname}
		</if>
		<if test="pname != null and pname != ''">
		    and a.pname = #{pname}
		</if>
		<if test="paddr != null and paddr != ''">
		    and a.paddr = #{paddr}
		</if>
		<if test="by1 != null and by1 != ''">
		    and a.by1 = #{by1}
		</if>
		<if test="by2 != null and by2 != ''">
		    and a.by2 = #{by2}
		</if>
		<if test="by3 != null and by3 != ''">
		    and a.by3 = #{by3}
		</if>
		<if test="by4 != null and by4 != ''">
		    and a.by4 = #{by4}
		</if>
		<if test="by5 != null and by5 != ''">
		    and a.by5 = #{by5}
		</if>
		<if test="by6 != null and by6 != ''">
		    and a.by6 = #{by6}
		</if>
		<if test="by7 != null and by7 != ''">
		    and a.by7 = #{by7}
		</if>
		<if test="by8 != null and by8 != ''">
		    and a.by8 = #{by8}
		</if>
		<if test="by9 != null and by9 != ''">
		    and a.by9 = #{by9}
		</if>
		<if test="by10 != null and by10 != ''">
		    and a.by10 = #{by10}
		</if>
		<if test="lname != null and lname != ''">
		    and a.lname = #{lname}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} pid desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from projects a  
		<where>
      		<if test="pid != null and pid !=0 ">
		    and a.pid = #{pid}
		</if>
		<if test="ptype != null and ptype != ''">
		    and a.ptype = #{ptype}
		</if>
		<if test="dtype != null and dtype != ''">
		    and a.dtype = #{dtype}
		</if>
		<if test="pflag != null and pflag != ''">
		    and a.pflag = #{pflag}
		</if>
		<if test="pno != null and pno != ''">
		    and a.pno = #{pno}
		</if>
		<if test="daname != null and daname != ''">
		    and a.daname = #{daname}
		</if>
		<if test="pname != null and pname != ''">
		    and a.pname = #{pname}
		</if>
		<if test="paddr != null and paddr != ''">
		    and a.paddr = #{paddr}
		</if>
		<if test="by1 != null and by1 != ''">
		    and a.by1 = #{by1}
		</if>
		<if test="by2 != null and by2 != ''">
		    and a.by2 = #{by2}
		</if>
		<if test="by3 != null and by3 != ''">
		    and a.by3 = #{by3}
		</if>
		<if test="by4 != null and by4 != ''">
		    and a.by4 = #{by4}
		</if>
		<if test="by5 != null and by5 != ''">
		    and a.by5 = #{by5}
		</if>
		<if test="by6 != null and by6 != ''">
		    and a.by6 = #{by6}
		</if>
		<if test="by7 != null and by7 != ''">
		    and a.by7 = #{by7}
		</if>
		<if test="by8 != null and by8 != ''">
		    and a.by8 = #{by8}
		</if>
		<if test="by9 != null and by9 != ''">
		    and a.by9 = #{by9}
		</if>
		<if test="by10 != null and by10 != ''">
		    and a.by10 = #{by10}
		</if>
		<if test="lname != null and lname != ''">
		    and a.lname = #{lname}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryProjectsById" parameterType="int" resultType="Projects">
    select  *  
     from projects a  	 where a.pid=#{value}
  </select>
 
	<insert id="insertProjects" useGeneratedKeys="true" keyProperty="pid" parameterType="Projects">
    insert into projects
    (ptype,dtype,pflag,pno,daname,pname,paddr,by1,by2,by3,by4,by5,by6,by7,by8,by9,by10,by11,by12,by13,by14,lname,by15,by16)
    values
    (#{ptype},#{dtype},#{pflag},#{pno},#{daname},#{pname},#{paddr},#{by1},#{by2},#{by3},#{by4},#{by5},#{by6},#{by7},#{by8},#{by9},#{by10},#{by11},#{by12},#{by13},#{by14},#{lname},#{by15},#{by16});
  </insert>
	
	<update id="updateProjects" parameterType="Projects" >
    update projects 
    <set>
		<if test="ptype != null and ptype != ''">
		    ptype = #{ptype},
		</if>
		<if test="dtype != null and dtype != ''">
		    dtype = #{dtype},
		</if>
		<if test="pflag != null and pflag != ''">
		    pflag = #{pflag},
		</if>
		<if test="pno != null and pno != ''">
		    pno = #{pno},
		</if>
		<if test="daname != null and daname != ''">
		    daname = #{daname},
		</if>
		<if test="pname != null and pname != ''">
		    pname = #{pname},
		</if>
		<if test="paddr != null and paddr != ''">
		    paddr = #{paddr},
		</if>
		<if test="by1 != null and by1 != ''">
		    by1 = #{by1},
		</if>
		<if test="by2 != null and by2 != ''">
		    by2 = #{by2},
		</if>
		<if test="by3 != null and by3 != ''">
		    by3 = #{by3},
		</if>
		<if test="by4 != null and by4 != ''">
		    by4 = #{by4},
		</if>
		<if test="by5 != null and by5 != ''">
		    by5 = #{by5},
		</if>
		<if test="by6 != null and by6 != ''">
		    by6 = #{by6},
		</if>
		<if test="by7 != null and by7 != ''">
		    by7 = #{by7},
		</if>
		<if test="by8 != null and by8 != ''">
		    by8 = #{by8},
		</if>
		<if test="by9 != null and by9 != ''">
		    by9 = #{by9},
		</if>
		<if test="by10 != null and by10 != ''">
		    by10 = #{by10},
		</if>
		<if test="by11 != null and by11 != ''">
		    by11 = #{by11},
		</if>
		<if test="by12 != null and by12 != ''">
		    by12 = #{by12},
		</if>
		<if test="by13 != null and by13 != ''">
		    by13 = #{by13},
		</if>
		<if test="by14 != null and by14 != ''">
		    by14 = #{by14},
		</if>
		<if test="lname != null and lname != ''">
		    lname = #{lname},
		</if>
		<if test="by15 != null and by15 != ''">
		    by15 = #{by15},
		</if>
		<if test="by16 != null and by16 != ''">
		    by16 = #{by16},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="pid != null or pid != ''">
      pid=#{pid}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteProjects" parameterType="int">
    delete from  projects where pid=#{value}
  </delete>

	
	
</mapper>

 
