<?xml version="1.0" encoding="UTF-8"?>
<module version="4">
  <component name="FacetManager">
    <facet type="JRebel" name="JRebel">
      <configuration>
        <option name="ideModuleStorage">
          <map>
            <entry key="com.zeroturnaround.jrebel.FormatVersion" value="7.0.0" />
            <entry key="jrebelEnabled" value="true" />
            <entry key="lastExternalPluginCheckTime" value="1749093703897" />
            <entry key="rebelXmlGenerationInvariantToken" value="PGFwcGxpY2F0aW9uIGdlbmVyYXRlZC1ieT0iaW50ZWxsaWoiPjxpZD5hdXRvMjAyNS1TZXJ2ZXI8L2lkPjxjbGFzc3BhdGg+PGRpciBuYW1lPSJKOi9hdXRvMjAyNS9hdXRvMjAyNS1zZXJ2ZXIvdGFyZ2V0L2NsYXNzZXMiPjwvZGlyPjwvY2xhc3NwYXRoPjwvYXBwbGljYXRpb24+" />
            <entry key="rebelXmlGenerationType" value="ide" />
          </map>
        </option>
        <option name="version" value="15" />
      </configuration>
    </facet>
  </component>
</module>