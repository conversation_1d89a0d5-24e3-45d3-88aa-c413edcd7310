package com.service;

import com.mapper.SmallMapper;
import com.model.Small;
import com.model.Tables;
import com.model.Mores;
import com.model.Projects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 项目生成服务实现类
 */
@Service
public class ProjectGeneratorServiceImpl implements ProjectGeneratorService {

    @Autowired
    private SmallMapper smallMapper;

    @Autowired
    private TablesService tablesService;

    @Autowired
    private MoresService moresService;

    @Autowired
    private CrudPageGeneratorService crudPageGeneratorService;

    @Autowired
    private ProjectsService projectsService;

    // 项目根目录
    private static final String PROJECT_ROOT = System.getProperty("user.dir");
    private static final String UPLOAD_DIR = PROJECT_ROOT + File.separator + "upload" + File.separator + "jg";
    private static final String TEMPLATE_DIR = PROJECT_ROOT + File.separator + "moban" + File.separator + "代码模板"
            + File.separator + "springboot";
    private static final String PROJECT_TEMPLATE_DIR = PROJECT_ROOT + File.separator + "moban" + File.separator + "项目模版"
            + File.separator + "springboot";

    @Override
    public Map<String, Object> generateProject(Map<String, Object> projectData) throws Exception {
        String projectNumber = (String) projectData.get("projectNumber");
        String databaseName = (String) projectData.get("databaseName");
        String projectName = (String) projectData.get("projectName");
        String packageName = (String) projectData.get("packageName");
        String backendTemplate = (String) projectData.get("backendTemplate");

        // 获取项目ID，如果没有则尝试从项目编号查找
        Integer projectId = null;
        if (projectData.get("projectId") != null) {
            projectId = (Integer) projectData.get("projectId");
        } else if (projectNumber != null && !projectNumber.trim().isEmpty()) {
            // 根据项目编号查找项目ID
            try {
                Projects queryProject = new Projects();
                queryProject.setPno(projectNumber);
                List<Projects> projectsList = projectsService.queryProjectsList(queryProject, null);
                if (projectsList != null && !projectsList.isEmpty()) {
                    projectId = projectsList.get(0).getPid();
                }
            } catch (Exception e) {
                System.err.println("根据项目编号查找项目ID失败: " + e.getMessage());
            }
        }

        // 将projectId添加到projectData中，供后续使用
        projectData.put("projectId", projectId);

        System.out.println("项目编号: " + projectNumber + ", 项目ID: " + projectId);

        // 构建项目路径
        String projectPath = projectNumber + databaseName;

        // 1. 创建项目目录
        Map<String, Object> dirResult = createProjectDirectory(projectNumber, databaseName, "springboot-thymeleaf",
                backendTemplate);
        if (!(Boolean) dirResult.get("success")) {
            throw new Exception((String) dirResult.get("message"));
        }

        // 2. 生成代码文件
        Map<String, String> generatedFiles = generateCodeFilesNew(projectData);
        System.out.println("生成的文件数量: " + generatedFiles.size());

        // 3. 保存生成的文件
        Map<String, Object> saveResult = saveGeneratedFiles(projectPath, generatedFiles);

        // 4. 压缩项目
        Map<String, Object> compressResult = compressProject(projectPath);

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("projectPath", projectPath);
        result.put("files", saveResult);
        result.put("compression", compressResult);

        return result;
    }

    @Override
    public Map<String, Object> createProjectDirectory(String projectNumber, String databaseName, String projectType,
            String templateId) throws Exception {
        Map<String, Object> result = new HashMap<>();

        try {
            // 创建项目目录名：项目编号 + 数据库名称
            String projectDirName = projectNumber + databaseName;
            Path projectPath = Paths.get(UPLOAD_DIR, projectDirName);

            // 如果目录已存在，先删除
            if (Files.exists(projectPath)) {
                deleteDirectory(projectPath);
            }

            // 创建项目目录
            Files.createDirectories(projectPath);

            // 如果是SpringBoot + Thymeleaf项目，复制项目模板
            if ("springboot-thymeleaf".equals(projectType)) {
                // 1. 先复制项目模板（基础项目结构）
                Path projectTemplatePath = Paths.get(PROJECT_TEMPLATE_DIR);
                if (Files.exists(projectTemplatePath)) {
                    copyProjectTemplate(projectTemplatePath, projectPath);
                } else {
                    result.put("success", false);
                    result.put("message", "SpringBoot项目模板目录不存在");
                    return result;
                }

                // 2. 处理后台模板（UI模板）
                if (templateId != null && !templateId.trim().isEmpty()) {
                    // 获取项目ID用于检查表功能配置
                    Integer projectId = getProjectIdFromPath(projectDirName);
                    processBackendTemplate(templateId, projectPath, projectId);
                }
            }

            result.put("success", true);
            result.put("message", "项目目录创建成功");
            result.put("projectPath", projectDirName);

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "创建项目目录失败：" + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> saveGeneratedFiles(String projectPath, Map<String, String> files) throws Exception {
        Map<String, Object> result = new HashMap<>();

        try {
            Path projectDir = Paths.get(UPLOAD_DIR, projectPath);

            if (!Files.exists(projectDir)) {
                result.put("success", false);
                result.put("message", "项目目录不存在");
                return result;
            }

            int savedCount = 0;
            List<String> savedFiles = new ArrayList<>();
            List<Map<String, Object>> failedFiles = new ArrayList<>();

            // 处理每个文件
            for (Map.Entry<String, String> entry : files.entrySet()) {
                String fileName = entry.getKey();
                String fileContent = entry.getValue();

                try {
                    // 确定文件目标路径
                    Path filePath = getFileTargetPath(projectDir, fileName);

                    // 确保目录存在
                    Path fileDir = filePath.getParent();
                    if (!Files.exists(fileDir)) {
                        Files.createDirectories(fileDir);
                    }

                    // 保存文件（UTF-8编码）
                    Files.write(filePath, fileContent.getBytes(StandardCharsets.UTF_8));
                    savedCount++;
                    savedFiles.add(fileName);

                } catch (Exception ex) {
                    Map<String, Object> failedFile = new HashMap<>();
                    failedFile.put("fileName", fileName);
                    failedFile.put("error", ex.getMessage());
                    failedFile.put("errorType", ex.getClass().getSimpleName());
                    failedFiles.add(failedFile);
                }
            }

            // 准备详细响应
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("savedCount", savedCount);
            responseData.put("failedCount", failedFiles.size());
            responseData.put("totalCount", files.size());
            responseData.put("savedFiles", savedFiles);
            responseData.put("failedFiles", failedFiles);

            if (savedCount > 0) {
                String message = String.format("成功保存 %d 个文件", savedCount);
                if (!failedFiles.isEmpty()) {
                    message += String.format("，%d 个文件失败", failedFiles.size());
                }
                result.put("success", true);
                result.put("message", message);
                result.put("data", responseData);
            } else {
                result.put("success", false);
                result.put("message", "没有文件被成功保存");
                result.put("data", responseData);
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "保存文件失败：" + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> compressProject(String projectPath) throws Exception {
        Map<String, Object> result = new HashMap<>();

        try {
            Path projectDir = Paths.get(UPLOAD_DIR, projectPath);

            if (!Files.exists(projectDir)) {
                result.put("success", false);
                result.put("message", "项目目录不存在");
                return result;
            }

            // ZIP文件路径
            String zipFileName = projectPath + ".zip";
            Path zipFilePath = Paths.get(UPLOAD_DIR, zipFileName);

            // 删除已存在的ZIP文件
            if (Files.exists(zipFilePath)) {
                Files.delete(zipFilePath);
            }

            // 创建ZIP文件
            try (FileOutputStream fos = new FileOutputStream(zipFilePath.toFile());
                    ZipOutputStream zos = new ZipOutputStream(fos)) {

                compressFolder(projectDir, zos, projectDir.toString().length() + 1);
            }

            // 获取ZIP文件大小
            long fileSize = Files.size(zipFilePath);
            String formattedSize = formatFileSize(fileSize);

            Map<String, Object> data = new HashMap<>();
            data.put("zipFileName", zipFileName);
            data.put("zipFilePath", "upload/jg/" + zipFileName);
            data.put("fileSize", formattedSize);
            data.put("projectPath", projectPath);

            result.put("success", true);
            result.put("message", "项目压缩成功");
            result.put("data", data);

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "压缩失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 生成代码文件（临时实现，后续需要集成模板引擎）
     */
    private Map<String, String> generateCodeFiles(Map<String, Object> projectData) {
        Map<String, String> files = new HashMap<>();

        String projectName = (String) projectData.get("projectName");
        String packageName = (String) projectData.get("packageName");
        String databaseName = (String) projectData.get("databaseName");

        // 生成application.properties
        String appProperties = String.format(
                "# %s 配置文件\n" +
                        "server.port=8080\n" +
                        "spring.datasource.url=***********************************************************************************************"
                        +
                        "spring.datasource.username=root\n" +
                        "spring.datasource.password=root\n" +
                        "spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver\n" +
                        "\n" +
                        "# MyBatis配置\n" +
                        "mybatis.mapper-locations=classpath:com/mapper/*.xml\n" +
                        "mybatis.type-aliases-package=%s.model\n",
                projectName, databaseName, packageName);
        files.put("application.properties", appProperties);

        // 生成pom.xml（简化版）
        String pomXml = String.format(
                "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                        "<project xmlns=\"http://maven.apache.org/POM/4.0.0\"\n" +
                        "         xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n" +
                        "         xsi:schemaLocation=\"http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd\">\n"
                        +
                        "    <modelVersion>4.0.0</modelVersion>\n" +
                        "    <groupId>%s</groupId>\n" +
                        "    <artifactId>%s</artifactId>\n" +
                        "    <version>1.0.0</version>\n" +
                        "    <packaging>jar</packaging>\n" +
                        "    <name>%s</name>\n" +
                        "    <description>%s</description>\n" +
                        "</project>",
                packageName, databaseName, projectName, projectName);
        files.put("pom.xml", pomXml);

        return files;
    }

    /**
     * 递归删除目录
     */
    private void deleteDirectory(Path path) throws IOException {
        if (Files.exists(path)) {
            Files.walk(path)
                    .sorted(Comparator.reverseOrder())
                    .map(Path::toFile)
                    .forEach(File::delete);
        }
    }

    /**
     * 递归复制目录
     */
    private void copyDirectory(Path sourceDir, Path destDir) throws IOException {
        Files.walk(sourceDir)
                .forEach(source -> {
                    try {
                        Path destination = destDir.resolve(sourceDir.relativize(source));
                        if (Files.isDirectory(source)) {
                            if (!Files.exists(destination)) {
                                Files.createDirectories(destination);
                            }
                        } else {
                            Files.copy(source, destination, StandardCopyOption.REPLACE_EXISTING);
                        }
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                });
    }

    /**
     * 复制项目模板（排除模板文件）
     */
    private void copyProjectTemplate(Path sourceDir, Path destDir) throws IOException {
        Files.walk(sourceDir)
                .forEach(source -> {
                    try {
                        Path destination = destDir.resolve(sourceDir.relativize(source));
                        if (Files.isDirectory(source)) {
                            if (!Files.exists(destination)) {
                                Files.createDirectories(destination);
                            }
                        } else {
                            String fileName = source.getFileName().toString();
                            // 排除模板文件，只复制项目基础结构文件
                            if (!isTemplateFile(fileName)) {
                                Files.copy(source, destination, StandardCopyOption.REPLACE_EXISTING);
                            }
                        }
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                });
    }

    /**
     * 判断是否为模板文件（这些文件不需要复制，将由代码生成器生成）
     */
    private boolean isTemplateFile(String fileName) {
        // 这些文件在项目模板中存在，但我们不需要复制它们
        // 因为它们将由代码生成器根据数据库表结构动态生成
        String[] excludeFiles = {
                // 代码模板文件（这些在代码模板目录中，不在项目模板中）
                "Admin.java",
                "AdminAction.java",
                "AdminMapper.java",
                "AdminService.java",
                "AdminServiceImpl.java",
                "adminMapper.xml"
        };

        for (String excludeFile : excludeFiles) {
            if (fileName.equals(excludeFile)) {
                return true;
            }
        }

        // 不排除项目模板中的基础文件，这些都需要复制
        return false;
    }

    /**
     * 从项目路径获取项目ID
     */
    private Integer getProjectIdFromPath(String projectDirName) {
        try {
            // 项目目录名格式：项目编号 + 数据库名称
            // 需要从项目编号查找项目ID
            // 这里假设项目编号是前4位数字
            if (projectDirName.length() >= 4) {
                String projectNumber = projectDirName.substring(0, 4);
                Projects queryProject = new Projects();
                queryProject.setPno(projectNumber);
                List<Projects> projectsList = projectsService.queryProjectsList(queryProject, null);
                if (projectsList != null && !projectsList.isEmpty()) {
                    return projectsList.get(0).getPid();
                }
            }
        } catch (Exception e) {
            System.err.println("从项目路径获取项目ID失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 处理后台模板
     */
    private void processBackendTemplate(String templateId, Path projectPath, Integer projectId) throws Exception {
        try {
            // 获取后台模板文件夹名称
            String backendTemplateFolderName = getBackendTemplateFolderName(templateId);
            if (backendTemplateFolderName == null || backendTemplateFolderName.trim().isEmpty()) {
                return; // 跳过如果没有找到后台模板
            }

            Path backendTemplatePath = Paths.get(PROJECT_ROOT, "moban", "后台模板", backendTemplateFolderName);
            if (!Files.exists(backendTemplatePath)) {
                return; // 跳过如果后台模板目录不存在
            }

            // 创建admin静态目录
            Path adminStaticPath = projectPath.resolve("src").resolve("main").resolve("resources")
                    .resolve("static").resolve("admin");
            if (!Files.exists(adminStaticPath)) {
                Files.createDirectories(adminStaticPath);
            }

            // 复制静态资源
            copyStaticResources(backendTemplatePath, adminStaticPath);

            // 创建templates目录
            Path templatesPath = projectPath.resolve("src").resolve("main").resolve("resources")
                    .resolve("templates");
            if (!Files.exists(templatesPath)) {
                Files.createDirectories(templatesPath);
            }

            // 处理HTML模板
            processHtmlTemplates(backendTemplatePath, templatesPath, templateId, projectId);

        } catch (Exception ex) {
            // 记录错误但不让整个过程失败
            System.err.println("处理后台模板失败: " + ex.getMessage());
        }
    }

    /**
     * 获取后台模板文件夹名称
     */
    private String getBackendTemplateFolderName(String templateId) {
        try {
            Small small = smallMapper.querySmallById(Integer.parseInt(templateId));
            return small != null ? small.getMemo1() : "";
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 复制静态资源
     */
    private void copyStaticResources(Path backendTemplatePath, Path adminStaticPath) throws IOException {
        String[] staticFolders = { "css", "image", "js", "images" };

        for (String folderName : staticFolders) {
            Path sourcePath = backendTemplatePath.resolve(folderName);
            Path destPath = adminStaticPath.resolve(folderName);

            if (Files.exists(sourcePath)) {
                copyDirectory(sourcePath, destPath);
            }
        }
    }

    /**
     * 处理HTML模板
     */
    private void processHtmlTemplates(Path backendTemplatePath, Path templatesPath, String templateId,
            Integer projectId) throws IOException {
        // 获取模板数据
        Map<String, String> templateData = getTemplateData(templateId, projectId);

        // 检查项目中所有表的功能配置
        Map<String, Integer> projectFunctions = checkProjectTableFunctions(projectId);

        // 处理login.html
        Path loginTemplatePath = backendTemplatePath.resolve("login.html");
        if (Files.exists(loginTemplatePath)) {
            String loginContent = new String(Files.readAllBytes(loginTemplatePath), StandardCharsets.UTF_8);
            loginContent = processTemplateVariables(loginContent, templateData, projectFunctions, projectId);

            Path loginOutputPath = templatesPath.resolve("login.html");
            Files.write(loginOutputPath, loginContent.getBytes(StandardCharsets.UTF_8));
        }

        // 处理管理员布局模板
        processAdminLayoutTemplates(backendTemplatePath, templatesPath, templateData, projectFunctions, projectId);
    }

    /**
     * 检查项目中所有表的功能配置
     */
    private Map<String, Integer> checkProjectTableFunctions(Integer projectId) {
        Map<String, Integer> functionCounts = new HashMap<>();
        functionCounts.put("backendLogin", 0);
        functionCounts.put("backendRegister", 0);
        functionCounts.put("backendPassword", 0);

        if (projectId == null) {
            return functionCounts;
        }

        try {
            // 获取项目的所有表
            Tables queryTables = new Tables();
            queryTables.setPid(projectId);
            List<Tables> tablesList = tablesService.queryTablesList(queryTables, null);

            if (tablesList != null) {
                for (Tables table : tablesList) {
                    Map<String, Boolean> tableFunctions = parseTableFunctions(table.getTgn());

                    if (tableFunctions.getOrDefault("backendLogin", false)) {
                        functionCounts.put("backendLogin", functionCounts.get("backendLogin") + 1);
                    }
                    if (tableFunctions.getOrDefault("backendRegister", false)) {
                        functionCounts.put("backendRegister", functionCounts.get("backendRegister") + 1);
                    }
                    if (tableFunctions.getOrDefault("backendPassword", false)) {
                        functionCounts.put("backendPassword", functionCounts.get("backendPassword") + 1);
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("检查项目表功能配置失败: " + e.getMessage());
        }

        return functionCounts;
    }

    /**
     * 获取模板数据
     */
    private Map<String, String> getTemplateData(String templateId, Integer projectId) {
        Map<String, String> templateData = new HashMap<>();

        try {
            Small small = smallMapper.querySmallById(Integer.parseInt(templateId));
            if (small != null) {
                // 根据small表结构映射字段（根据实际数据修正）
                // memo1: 模板文件夹名称 (如: mb100)
                templateData.put("templateFolder", small.getMemo1() != null ? small.getMemo1() : "");

                // memo2: 菜单项HTML内容（包含$lanmu$、$navmenu$变量）
                templateData.put("menuItem", small.getMemo2() != null ? small.getMemo2() : "");

                // memo3: 子菜单项HTML内容（包含$url$、$mch$变量）
                templateData.put("subMenuItem", small.getMemo3() != null ? small.getMemo3() : "");

                // memo4: 图片内容
                templateData.put("imageContent", small.getMemo4() != null ? small.getMemo4() : "");

                // by1: 身份验证相关内容（$$shenfen$$）
                // 如果只有一个表有后台登录功能，直接设置为空
                Map<String, Integer> projectFunctions = checkProjectTableFunctions(projectId);
                if (projectFunctions.getOrDefault("backendLogin", 0) <= 1) {
                    templateData.put("shenfen", "");
                } else {
                    templateData.put("shenfen", small.getBy1() != null ? small.getBy1() : "");
                }

                // by2: 登录表单HTML内容（$$login$$）
                templateData.put("login", small.getBy2() != null ? small.getBy2() : "");

                // by3: 按钮样式
                templateData.put("buttonStyle", small.getBy3() != null ? small.getBy3() : "");

                // by4: 脚本内容
                templateData.put("scripts", small.getBy4() != null ? small.getBy4() : "");

                // by5: 注册相关（$$reg$$）
                templateData.put("zhuce", small.getBy5() != null ? small.getBy5() : "");

                // by6: 仪表板内容
                templateData.put("dashboard", small.getBy6() != null ? small.getBy6() : "");

                // 兼容旧的字段名（根据实际数据结构修正）
                // 从memo2中提取$lanmu$变量的值，这里暂时使用空值，实际应该解析HTML
                templateData.put("lanmu", ""); // 栏目名称，需要从菜单HTML中提取
                templateData.put("navmenu", ""); // 导航菜单，需要从菜单HTML中提取
                templateData.put("url", ""); // 链接地址
                templateData.put("mch", ""); // 菜单项文本
                templateData.put("fname", small.getMemo3() != null ? small.getMemo3() : "");
                templateData.put("addr3", small.getMemo4() != null ? small.getMemo4() : "");
            }
        } catch (Exception e) {
            System.err.println("获取模板数据失败: " + e.getMessage());
        }

        return templateData;
    }

    /**
     * 处理模板变量
     */
    private String processTemplateVariables(String content, Map<String, String> templateData,
            Map<String, Integer> projectFunctions, Integer projectId) {
        for (Map.Entry<String, String> entry : templateData.entrySet()) {
            content = content.replace("$" + entry.getKey() + "$", entry.getValue());
        }

        // 替换通用模板变量
        content = content.replace("$$houtaishouye$$", "toright");

        // 生成动态菜单
        String menuContent = generateDynamicMenu(projectId, templateData);
        content = content.replace("$$menu$$", menuContent);

        // $$login$$使用by2字段的内容（完整的登录表单）
        String loginContent = templateData.getOrDefault("login", "");
        content = content.replace("$$login$$", loginContent);

        // $$reg$$只有在项目中有表选择了后台注册功能时才显示
        String regContent = "";
        if (projectFunctions.getOrDefault("backendRegister", 0) > 0) {
            regContent = generateBootstrapRegistrationModal(projectId);
        }
        content = content.replace("$$reg$$", regContent);

        // $zhuce$变量处理：如果有注册功能显示"注册"，否则为空
        String zhuceText = "";
        if (projectFunctions.getOrDefault("backendRegister", 0) > 0) {
            zhuceText = "注册";
        }
        content = content.replace("$zhuce$", zhuceText);

        // $$shenfen$$只有在多个表都有后台登录功能时才显示
        String shenfenContent = "";
        if (projectFunctions.getOrDefault("backendLogin", 0) > 1) {
            // 生成后台登录表的中文名称单选框
            shenfenContent = generateBackendLoginRadioButtons(projectId);
        }
        content = content.replace("$$shenfen$$", shenfenContent);

        content = content.replace("$$quit$$", "toquit");
        content = content.replace("$memo$", getDashboardContent(templateData));

        // 替换用户名和身份显示为Thymeleaf表达式
        content = replaceUserDisplayVariables(content, projectId);

        // 生成注册页面路径
        String registrationPagePath = generateRegistrationPagePath(projectId);
        content = content.replace("$fname$", registrationPagePath);

        // 替换 $addr3$ 为静态资源路径
        content = content.replace("$addr3$", "/admin/");

        // 更新iframe源指向dashboard
        content = content.replace("src=\"right.html\"", "src=\"toright\"");

        // 更新CSS和JS路径使用admin静态文件夹
        content = content.replace("href=\"css/", "href=\"/admin/css/");
        content = content.replace("src=\"js/", "src=\"/admin/js/");
        content = content.replace("src=\"image/", "src=\"/admin/image/");

        // 如果是head.html文件，添加Bootstrap Icons的JavaScript代码并替换Font Awesome图标
        content = processHeadHtmlContent(content);

        return content;
    }

    /**
     * 生成Bootstrap注册模态框
     */
    private String generateBootstrapRegistrationModal(Integer projectId) {
        try {
            // 获取项目的所有表
            Tables queryTables = new Tables();
            queryTables.setPid(projectId);
            List<Tables> tablesList = tablesService.queryTablesList(queryTables, null);

            if (tablesList != null) {
                for (Tables table : tablesList) {
                    Map<String, Boolean> tableFunctions = parseTableFunctions(table.getTgn());

                    // 如果这个表有注册功能，生成Bootstrap Modal
                    if (tableFunctions.getOrDefault("backendRegister", false)) {
                        String tableName = table.getTname();
                        String registrationUrl = "/to" + toCamelCase(tableName) + "Reg";

                        return generateBootstrapModalScript(registrationUrl);
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("生成Bootstrap注册模态框失败: " + e.getMessage());
        }

        // 如果没有找到注册功能的表，返回空字符串
        return "";
    }

    /**
     * 生成Bootstrap Modal脚本
     */
    private String generateBootstrapModalScript(String registrationUrl) {
        return "<script>\n" +
               "        function showRegistration() {\n" +
               "            // 创建Bootstrap Modal\n" +
               "            var modalId = 'registrationModal-' + Date.now();\n" +
               "            var modalHtml =\n" +
               "                '<div class=\"modal fade\" id=\"' + modalId + '\" tabindex=\"-1\" aria-hidden=\"true\">' +\n" +
               "                '<div class=\"modal-dialog modal-dialog-centered modal-lg\">' +\n" +
               "                '<div class=\"modal-content\">' +\n" +
               "                '<div class=\"modal-header\">' +\n" +
               "                '<h5 class=\"modal-title\">' +\n" +
               "                '<i class=\"bi bi-person-plus me-2\"></i>用户注册' +\n" +
               "                '</h5>' +\n" +
               "                '<button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\"></button>' +\n" +
               "                '</div>' +\n" +
               "                '<div class=\"modal-body p-0\">' +\n" +
               "                '<iframe src=\"" + registrationUrl + "\" style=\"width: 100%; height: 400px; border: none;\"></iframe>' +\n" +
               "                '</div>' +\n" +
               "                '</div>' +\n" +
               "                '</div>' +\n" +
               "                '</div>';\n" +
               "\n" +
               "            // 添加modal到页面\n" +
               "            $('body').append(modalHtml);\n" +
               "            var modal = new bootstrap.Modal(document.getElementById(modalId));\n" +
               "\n" +
               "            // 显示modal\n" +
               "            modal.show();\n" +
               "\n" +
               "            // modal关闭后移除DOM元素\n" +
               "            $('#' + modalId).on('hidden.bs.modal', function () {\n" +
               "                $(this).remove();\n" +
               "            });\n" +
               "        }\n" +
               "    </script>\n" +
               "    <div class=\"text-center mt-4\">\n" +
               "        <a onclick=\"showRegistration();\" class=\"text-decoration-none\">注册</a>\n" +
               "    </div>";
    }

    /**
     * 生成注册页面路径
     */
    private String generateRegistrationPagePath(Integer projectId) {
        try {
            // 获取项目的所有表
            Tables queryTables = new Tables();
            queryTables.setPid(projectId);
            List<Tables> tablesList = tablesService.queryTablesList(queryTables, null);

            if (tablesList != null) {
                for (Tables table : tablesList) {
                    Map<String, Boolean> tableFunctions = parseTableFunctions(table.getTgn());

                    // 如果这个表有注册功能，返回注册页面路径
                    if (tableFunctions.getOrDefault("backendRegister", false)) {
                        String tableName = table.getTname();
                        return "/to" + toCamelCase(tableName) + "Reg";
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("生成注册页面路径失败: " + e.getMessage());
        }

        // 如果没有找到注册功能的表，返回空字符串
        return "";
    }

    /**
     * 处理head.html文件内容，添加Bootstrap Icons的JavaScript代码并替换Font Awesome图标
     */
    private String processHeadHtmlContent(String content) {
        // 检查是否包含Bootstrap相关内容，如果包含则认为是head.html文件
        if (content.contains("bootstrap") || content.contains("Bootstrap")) {
            // 添加Font Awesome CDN链接（保持兼容性）
            if (!content.contains("font-awesome") && !content.contains("fontawesome")) {
            
                // 在Bootstrap Icons之后添加Font Awesome链接
                if (content.contains("bootstrap-icons.css")) {
                    content = content.replace("bootstrap-icons.css\" rel=\"stylesheet\">",
                                            "bootstrap-icons.css\" rel=\"stylesheet\">\n" );
                } else {
                    // 如果没有Bootstrap Icons，在Bootstrap CSS之后添加
                    content = content.replace("bootstrap.min.css\" rel=\"stylesheet\">",
                                            "bootstrap.min.css\" rel=\"stylesheet\">\n" );
                }
            }

            // 添加通用JavaScript方法，包含Bootstrap Icons图标
            String commonUtilsScript = generateCommonUtilsScript();

            // 在</script>标签之前添加通用方法
            if (content.contains("</script>")) {
                // 找到最后一个</script>标签的位置
                int lastScriptIndex = content.lastIndexOf("</script>");
                if (lastScriptIndex > 0) {
                    content = content.substring(0, lastScriptIndex) +
                             commonUtilsScript +
                             content.substring(lastScriptIndex);
                }
            } else {
                // 如果没有script标签，在文件末尾添加
                content += "\n\n" + commonUtilsScript;
            }
        }

        return content;
    }

    /**
     * 生成通用JavaScript方法，使用Bootstrap Icons
     */
    private String generateCommonUtilsScript() {
        return "\n    // 全局变量\n" +
               "    window.CommonUtils = {\n" +
               "        // Bootstrap Toast提示函数\n" +
               "        showToast: function (message, type, duration) {\n" +
               "            type = type || 'success';\n" +
               "            duration = duration || 3000;\n" +
               "\n" +
               "            var toastClass = '';\n" +
               "            var iconClass = '';\n" +
               "            var title = '';\n" +
               "\n" +
               "            switch (type) {\n" +
               "                case 'success':\n" +
               "                    toastClass = 'bg-success';\n" +
               "                    iconClass = 'bi bi-check-circle-fill';\n" +
               "                    title = '成功';\n" +
               "                    break;\n" +
               "                case 'error':\n" +
               "                case 'danger':\n" +
               "                    toastClass = 'bg-danger';\n" +
               "                    iconClass = 'bi bi-exclamation-circle-fill';\n" +
               "                    title = '错误';\n" +
               "                    break;\n" +
               "                case 'warning':\n" +
               "                    toastClass = 'bg-warning';\n" +
               "                    iconClass = 'bi bi-exclamation-triangle-fill';\n" +
               "                    title = '警告';\n" +
               "                    break;\n" +
               "                case 'info':\n" +
               "                    toastClass = 'bg-info';\n" +
               "                    iconClass = 'bi bi-info-circle-fill';\n" +
               "                    title = '提示';\n" +
               "                    break;\n" +
               "                default:\n" +
               "                    toastClass = 'bg-primary';\n" +
               "                    iconClass = 'bi bi-info-circle-fill';\n" +
               "                    title = '提示';\n" +
               "            }\n" +
               "\n" +
               "            var toastHtml =\n" +
               "                '<div class=\"toast align-items-center text-white ' + toastClass + ' border-0\" role=\"alert\" aria-live=\"assertive\" aria-atomic=\"true\">' +\n" +
               "                '<div class=\"d-flex\">' +\n" +
               "                '<div class=\"toast-body\">' +\n" +
               "                '<i class=\"' + iconClass + ' me-2\"></i>' + message +\n" +
               "                '</div>' +\n" +
               "                '<button type=\"button\" class=\"btn-close btn-close-white me-2 m-auto\" data-bs-dismiss=\"toast\"></button>' +\n" +
               "                '</div>' +\n" +
               "                '</div>';\n" +
               "\n" +
               "            var toastContainer = document.getElementById('toast-container');\n" +
               "            if (!toastContainer) {\n" +
               "                toastContainer = document.createElement('div');\n" +
               "                toastContainer.id = 'toast-container';\n" +
               "                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';\n" +
               "                document.body.appendChild(toastContainer);\n" +
               "            }\n" +
               "\n" +
               "            toastContainer.innerHTML = toastHtml;\n" +
               "            var toastElement = toastContainer.querySelector('.toast');\n" +
               "            var toast = new bootstrap.Toast(toastElement, { delay: duration });\n" +
               "            toast.show();\n" +
               "        },\n" +
               "\n" +
               "        // 确认删除对话框\n" +
               "        confirmDelete: function (message, callback) {\n" +
               "            message = message || '确定要删除这条记录吗？此操作不可恢复！';\n" +
               "\n" +
               "            // 创建Bootstrap Modal\n" +
               "            var modalId = 'deleteModal-' + Date.now();\n" +
               "            var modalHtml =\n" +
               "                '<div class=\"modal fade\" id=\"' + modalId + '\" tabindex=\"-1\" aria-hidden=\"true\">' +\n" +
               "                '<div class=\"modal-dialog modal-dialog-centered\">' +\n" +
               "                '<div class=\"modal-content\">' +\n" +
               "                '<div class=\"modal-header bg-danger text-white\">' +\n" +
               "                '<h5 class=\"modal-title\" style=\"font-weight: normal; font-size: 16px;\">' +\n" +
               "                '<i class=\"bi bi-exclamation-triangle-fill me-2\"></i>确认删除' +\n" +
               "                '</h5>' +\n" +
               "                '<button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\"></button>' +\n" +
               "                '</div>' +\n" +
               "                '<div class=\"modal-body\">' +\n" +
               "                '<div class=\"text-center\">' +\n" +
               "                '<i class=\"bi bi-trash3 text-danger\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>' +\n" +
               "                '<p style=\"font-size: 14px; font-weight: normal;\">' + message + '</p>' +\n" +
               "                '</div>' +\n" +
               "                '</div>' +\n" +
               "                '<div class=\"modal-footer\">' +\n" +
               "                '<button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">' +\n" +
               "                '<i class=\"bi bi-x-lg me-1\"></i>取消' +\n" +
               "                '</button>' +\n" +
               "                '<button type=\"button\" class=\"btn btn-danger\" id=\"confirmDeleteBtn\">' +\n" +
               "                '<i class=\"bi bi-trash3 me-1\"></i>确认删除' +\n" +
               "                '</button>' +\n" +
               "                '</div>' +\n" +
               "                '</div>' +\n" +
               "                '</div>' +\n" +
               "                '</div>';\n" +
               "\n" +
               "            // 添加modal到页面\n" +
               "            $('body').append(modalHtml);\n" +
               "            var modal = new bootstrap.Modal(document.getElementById(modalId));\n" +
               "\n" +
               "            // 绑定确认按钮事件\n" +
               "            $('#' + modalId + ' #confirmDeleteBtn').on('click', function () {\n" +
               "                modal.hide();\n" +
               "                if (typeof callback === 'function') {\n" +
               "                    callback();\n" +
               "                }\n" +
               "            });\n" +
               "\n" +
               "            // 显示modal\n" +
               "            modal.show();\n" +
               "\n" +
               "            // modal关闭后移除DOM元素\n" +
               "            $('#' + modalId).on('hidden.bs.modal', function () {\n" +
               "                $(this).remove();\n" +
               "            });\n" +
               "        }\n" +
               "    };\n";
    }

    /**
     * 获取仪表板内容
     */
    private String getDashboardContent() {
        return "<!-- 欢迎区域 -->\n" +
                "<div class=\"welcome-section\">\n" +
                "    <i class=\"bi bi-house-heart\" style=\"font-size: 3rem; margin-bottom: 15px;\"></i>\n" +
                "    <h2>欢迎使用</h2>\n" +
                "    <p class=\"mb-0\">系统管理后台</p>\n" +
                "</div>\n" +
                "\n" +
                "<!-- 统计卡片 -->\n" +
                "<div class=\"row\">\n" +
                "    <!-- 用户数量 -->\n" +
                "    <div class=\"col-xl-3 col-md-6 mb-4\">\n" +
                "        <div class=\"card dashboard-card card-1 animate-card\">\n" +
                "            <div class=\"card-body text-center\">\n" +
                "                <i class=\"bi bi-people-fill card-icon\"></i>\n" +
                "                <div class=\"card-number\">1,248</div>\n" +
                "                <div class=\"card-label\">用户数量</div>\n" +
                "            </div>\n" +
                "        </div>\n" +
                "    </div>\n" +
                "</div>";
    }

    /**
     * 获取仪表板内容（带模板数据）
     */
    private String getDashboardContent(Map<String, String> templateData) {
        String dashboardContent = templateData.getOrDefault("dashboard", "");
        if (dashboardContent != null && !dashboardContent.trim().isEmpty()) {
            return dashboardContent;
        }
        // 如果模板数据中没有仪表板内容，返回默认内容
        return getDashboardContent();
    }

    /**
     * 处理管理员布局模板
     */
    private void processAdminLayoutTemplates(Path backendTemplatePath, Path templatesPath,
            Map<String, String> templateData, Map<String, Integer> projectFunctions, Integer projectId)
            throws IOException {
        // 确保admin目录存在
        Path adminPath = templatesPath.resolve("admin");
        if (!Files.exists(adminPath)) {
            Files.createDirectories(adminPath);
        }

        // 处理index.html（主要管理员布局）- 放到admin目录中
        Path indexTemplatePath = backendTemplatePath.resolve("index.html");
        if (Files.exists(indexTemplatePath)) {
            String indexContent = new String(Files.readAllBytes(indexTemplatePath), StandardCharsets.UTF_8);
            indexContent = processTemplateVariables(indexContent, templateData, projectFunctions, projectId);

            Path indexOutputPath = adminPath.resolve("index.html");
            Files.write(indexOutputPath, indexContent.getBytes(StandardCharsets.UTF_8));
        }

        // 处理right.html（仪表板内容）- 放到admin目录中
        Path rightTemplatePath = backendTemplatePath.resolve("right.html");
        if (Files.exists(rightTemplatePath)) {
            String rightContent = new String(Files.readAllBytes(rightTemplatePath), StandardCharsets.UTF_8);
            rightContent = processTemplateVariables(rightContent, templateData, projectFunctions, projectId);

            Path rightOutputPath = adminPath.resolve("right.html");
            Files.write(rightOutputPath, rightContent.getBytes(StandardCharsets.UTF_8));
        }

        // 处理head.html（页面头部模板）
        Path headTemplatePath = backendTemplatePath.resolve("head.html");
        if (Files.exists(headTemplatePath)) {
            String headContent = new String(Files.readAllBytes(headTemplatePath), StandardCharsets.UTF_8);
            headContent = processTemplateVariables(headContent, templateData, projectFunctions, projectId);

            Path headOutputPath = adminPath.resolve("head.html");
            Files.write(headOutputPath, headContent.getBytes(StandardCharsets.UTF_8));
            System.out.println("复制head.html到项目模板目录");
        }
    }

    /**
     * 确定文件目标路径
     */
    private Path getFileTargetPath(Path projectDir, String fileName) {
        // 如果fileName已经包含路径，直接使用
        if (fileName.contains("/") || fileName.contains("\\")) {
            return projectDir.resolve(fileName.replace("/", File.separator));
        }

        // 否则根据文件类型确定路径
        String relativePath = "";

        if (fileName.endsWith(".java")) {
            // Java文件保存到src/main/java/com/目录
            if (fileName.endsWith("Action.java")) {
                relativePath = "src/main/java/com/controller/" + fileName;
            } else if (fileName.endsWith("Service.java") && !fileName.endsWith("ServiceImpl.java")) {
                relativePath = "src/main/java/com/service/" + fileName;
            } else if (fileName.endsWith("ServiceImpl.java")) {
                relativePath = "src/main/java/com/service/" + fileName;
            } else if (fileName.endsWith("Mapper.java")) {
                relativePath = "src/main/java/com/mapper/" + fileName;
            } else {
                // 实体类
                relativePath = "src/main/java/com/model/" + fileName;
            }
        } else if (fileName.endsWith(".xml")) {
            // 特殊处理不同的XML文件
            if ("pom.xml".equalsIgnoreCase(fileName)) {
                // pom.xml保存到项目根目录
                relativePath = fileName;
            } else {
                // 其他XML文件（Mapper XML）保存到src/main/resources/com/mapper/目录
                relativePath = "src/main/resources/com/mapper/" + fileName;
            }
        } else if (fileName.endsWith(".html") || fileName.endsWith(".jsp")) {
            // HTML/JSP文件保存到src/main/resources/templates/目录
            relativePath = "src/main/resources/templates/" + fileName;
        } else if (fileName.endsWith(".properties")) {
            // Properties文件保存到src/main/resources/目录
            relativePath = "src/main/resources/" + fileName;
        } else {
            // 其他文件保存到根目录
            relativePath = fileName;
        }

        return projectDir.resolve(relativePath);
    }

    /**
     * 压缩文件夹
     */
    private void compressFolder(Path path, ZipOutputStream zipStream, int folderOffset) throws IOException {
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(path)) {
            for (Path entry : stream) {
                if (Files.isRegularFile(entry)) {
                    String entryName = entry.toString().substring(folderOffset);
                    entryName = entryName.replace('\\', '/'); // 确保使用正斜杠

                    ZipEntry zipEntry = new ZipEntry(entryName);
                    zipEntry.setTime(Files.getLastModifiedTime(entry).toMillis());
                    zipEntry.setSize(Files.size(entry));
                    zipStream.putNextEntry(zipEntry);

                    try (InputStream inputStream = Files.newInputStream(entry)) {
                        byte[] buffer = new byte[4096];
                        int length;
                        while ((length = inputStream.read(buffer)) > 0) {
                            zipStream.write(buffer, 0, length);
                        }
                    }
                    zipStream.closeEntry();
                } else if (Files.isDirectory(entry)) {
                    compressFolder(entry, zipStream, folderOffset);
                }
            }
        }
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long bytes) {
        String[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.length - 1) {
            order++;
            len = len / 1024;
        }
        return String.format("%.2f %s", len, sizes[order]);
    }

    /**
     * 生成代码文件（新实现）
     */
    private Map<String, String> generateCodeFilesNew(Map<String, Object> projectData) throws Exception {
        Map<String, String> files = new HashMap<>();

        String projectName = (String) projectData.get("projectName");
        String packageName = (String) projectData.get("packageName");
        String databaseName = (String) projectData.get("databaseName");
        String databaseType = (String) projectData.get("databaseType");
        String backendTemplate = (String) projectData.get("backendTemplate");
        Integer projectId = (Integer) projectData.get("projectId");

        // 如果没有指定数据库类型，默认使用MySQL
        if (databaseType == null || databaseType.trim().isEmpty()) {
            databaseType = "mysql";
        }

        // 生成项目级别的配置文件
        files.putAll(generateProjectFiles(projectName, packageName, databaseName, databaseType));

        // 如果有项目ID，生成表相关的代码文件
        if (projectId != null) {
            files.putAll(generateTableFiles(projectId, packageName));
        }

        // 生成HTML模板文件
        if (backendTemplate != null && !backendTemplate.trim().isEmpty() && projectId != null) {
            files.putAll(generateHtmlTemplateFiles(backendTemplate, projectId));
        }

        return files;
    }

    /**
     * 生成HTML模板文件
     */
    private Map<String, String> generateHtmlTemplateFiles(String templateId, Integer projectId) throws Exception {
        Map<String, String> files = new HashMap<>();

        try {
            // 获取后台模板文件夹名称
            String backendTemplateFolderName = getBackendTemplateFolderName(templateId);
            if (backendTemplateFolderName == null || backendTemplateFolderName.trim().isEmpty()) {
                return files; // 跳过如果没有找到后台模板
            }

            Path backendTemplatePath = Paths.get(PROJECT_ROOT, "moban", "后台模板", backendTemplateFolderName);
            if (!Files.exists(backendTemplatePath)) {
                return files; // 跳过如果后台模板目录不存在
            }

            // 获取模板数据
            Map<String, String> templateData = getTemplateData(templateId, projectId);

            // 检查项目中所有表的功能配置
            Map<String, Integer> projectFunctions = checkProjectTableFunctions(projectId);

            // 生成login.html
            Path loginTemplatePath = backendTemplatePath.resolve("login.html");
            if (Files.exists(loginTemplatePath)) {
                String loginContent = new String(Files.readAllBytes(loginTemplatePath), StandardCharsets.UTF_8);
                loginContent = processTemplateVariables(loginContent, templateData, projectFunctions, projectId);
                files.put("src/main/resources/templates/login.html", loginContent);
            }

            // 生成index.html（主要管理员布局）- 放到admin目录中
            Path indexTemplatePath = backendTemplatePath.resolve("index.html");
            if (Files.exists(indexTemplatePath)) {
                String indexContent = new String(Files.readAllBytes(indexTemplatePath), StandardCharsets.UTF_8);
                indexContent = processTemplateVariables(indexContent, templateData, projectFunctions, projectId);
                files.put("src/main/resources/templates/admin/index.html", indexContent);
            }

            // 生成right.html（仪表板内容）- 放到admin目录中
            Path rightTemplatePath = backendTemplatePath.resolve("right.html");
            if (Files.exists(rightTemplatePath)) {
                String rightContent = new String(Files.readAllBytes(rightTemplatePath), StandardCharsets.UTF_8);
                rightContent = processTemplateVariables(rightContent, templateData, projectFunctions, projectId);
                files.put("src/main/resources/templates/admin/right.html", rightContent);
            }

            // 生成head.html（页面头部模板）
            Path headTemplatePath = backendTemplatePath.resolve("head.html");
            if (Files.exists(headTemplatePath)) {
                String headContent = new String(Files.readAllBytes(headTemplatePath), StandardCharsets.UTF_8);
                headContent = processTemplateVariables(headContent, templateData, projectFunctions, projectId);
                files.put("src/main/resources/templates/admin/head.html", headContent);
                System.out.println("生成head.html文件");
            }

            // 生成CRUD页面
            Map<String, String> crudPages = generateCrudPages(templateId, projectId, backendTemplatePath);
            files.putAll(crudPages);

        } catch (Exception e) {
            System.err.println("生成HTML模板文件失败: " + e.getMessage());
        }

        return files;
    }

    /**
     * 生成项目级别的配置文件
     */
    private Map<String, String> generateProjectFiles(String projectName, String packageName, String databaseName, String databaseType)
            throws Exception {
        Map<String, String> files = new HashMap<>();

        // 读取并处理application.properties模板（从代码模板目录）
        String appPropertiesTemplate = readCodeTemplateFile("application.properties");
        String appProperties = appPropertiesTemplate
                .replace("${projectName}", projectName)
                .replace("${databaseName}", databaseName)
                .replace("${packageName}", packageName)
                .replace("${driverClassName}", "com.mysql.cj.jdbc.Driver")
                .replace("${databaseUrl}",
                        "***************************/" + databaseName
                                + "?useUnicode=true&characterEncoding=utf8&serverTimezone=GMT%2B8")
                .replace("${databaseUsername}", "root")
                .replace("${databasePassword}", "root");
        files.put("src/main/resources/application.properties", appProperties);

        // 读取并处理pom.xml模板（从代码模板目录）
        String pomTemplate = readCodeTemplateFile("pom.xml");

        // 根据数据库类型生成相应的依赖
        String databaseDependency = generateDatabaseDependency(databaseType);

        String pomXml = pomTemplate
                .replace("${packageName}", packageName)
                .replace("${databaseName}", databaseName)
                .replace("${projectName}", projectName)
                .replace("${databaseDependency}", databaseDependency);
        files.put("pom.xml", pomXml);

        return files;
    }

    /**
     * 根据数据库类型生成相应的依赖
     */
    private String generateDatabaseDependency(String databaseType) {
        if (databaseType == null) {
            databaseType = "mysql";
        }

        switch (databaseType.toLowerCase()) {
            case "mysql":
                return "\t\t<!-- MySQL Connector -->\n" +
                       "\t\t<dependency>\n" +
                       "\t\t\t<groupId>mysql</groupId>\n" +
                       "\t\t\t<artifactId>mysql-connector-java</artifactId>\n" +
                       "\t\t\t<version>8.0.33</version>\n" +
                       "\t\t</dependency>";
            case "sqlserver":
                return "\t\t<!-- SQL Server Connector -->\n" +
                       "\t\t<dependency>\n" +
                       "\t\t\t<groupId>com.microsoft.sqlserver</groupId>\n" +
                       "\t\t\t<artifactId>mssql-jdbc</artifactId>\n" +
                       "\t\t\t<version>12.2.0.jre8</version>\n" +
                       "\t\t</dependency>";
            default:
                // 默认返回MySQL依赖
                return "\t\t<!-- MySQL Connector -->\n" +
                       "\t\t<dependency>\n" +
                       "\t\t\t<groupId>mysql</groupId>\n" +
                       "\t\t\t<artifactId>mysql-connector-java</artifactId>\n" +
                       "\t\t\t<version>8.0.33</version>\n" +
                       "\t\t</dependency>";
        }
    }

    /**
     * 为项目中的所有表生成代码文件
     */
    private Map<String, String> generateTableFiles(Integer projectId, String packageName) throws Exception {
        Map<String, String> files = new HashMap<>();

        if (projectId == null) {
            System.out.println("项目ID为空，无法生成表相关代码");
            return files;
        }

        System.out.println("开始为项目ID " + projectId + " 生成表代码");

        // 获取项目的所有表
        Tables queryTables = new Tables();
        queryTables.setPid(projectId);
        List<Tables> tablesList = tablesService.queryTablesList(queryTables, null);

        System.out.println("找到 " + (tablesList != null ? tablesList.size() : 0) + " 个表");

        if (tablesList == null || tablesList.isEmpty()) {
            System.out.println("项目中没有找到表，无法生成代码");
            return files;
        }

        // 为每个表生成代码文件
        for (Tables table : tablesList) {
            System.out.println("正在处理表: " + table.getTname() + " (ID: " + table.getTid() + ")");

            // 获取表的字段信息
            Mores queryMores = new Mores();
            queryMores.setTid(table.getTid());
            List<Mores> fieldsList = moresService.queryMoresList(queryMores, null);

            System.out.println("表 " + table.getTname() + " 有 " + (fieldsList != null ? fieldsList.size() : 0) + " 个字段");

            if (fieldsList != null && !fieldsList.isEmpty()) {
                // 生成该表的所有代码文件
                Map<String, String> tableFiles = generateSingleTableFiles(table, fieldsList, packageName);
                files.putAll(tableFiles);
                System.out.println("为表 " + table.getTname() + " 生成了 " + tableFiles.size() + " 个文件");
            } else {
                System.out.println("表 " + table.getTname() + " 没有字段，跳过代码生成");
            }
        }

        // 生成IndexAction.java（包含统一的登录方法）
        Map<String, String> indexActionFiles = generateIndexActionFile(projectId, packageName, tablesList);
        files.putAll(indexActionFiles);

        System.out.println("总共生成了 " + files.size() + " 个代码文件");
        return files;
    }

    /**
     * 生成IndexAction.java文件（包含统一的登录方法）
     */
    private Map<String, String> generateIndexActionFile(Integer projectId, String packageName, List<Tables> tablesList)
            throws Exception {
        Map<String, String> files = new HashMap<>();

        try {
            // 读取IndexAction.java模板
            String indexActionTemplate = readProjectTemplateFile("IndexAction.java");

            // 检查项目中所有表的功能配置
            Map<String, Integer> projectFunctions = checkProjectTableFunctions(projectId);

            // 生成登录方法
            String loginMethods = generateUnifiedLoginMethods(projectFunctions, tablesList);

            // 生成Service声明
            String serviceDeclarations = generateServiceDeclarations(tablesList);

            // 替换模板变量
            String indexActionCode = indexActionTemplate
                    .replace("${packageName}", packageName)
                    .replace("$shengming$", serviceDeclarations)
                    .replace("$mingcheng$", generateServiceAutowired(tablesList))
                    .replace("$memo$", loginMethods);

            files.put("src/main/java/com/controller/IndexAction.java", indexActionCode);
            System.out.println("生成IndexAction.java文件");

        } catch (Exception e) {
            System.err.println("生成IndexAction.java文件失败: " + e.getMessage());
            e.printStackTrace();
        }

        return files;
    }

    /**
     * 为单个表生成所有相关的代码文件
     */
    private Map<String, String> generateSingleTableFiles(Tables table, List<Mores> fields, String packageName)
            throws Exception {
        Map<String, String> files = new HashMap<>();

        try {
            System.out.println("开始为表 " + table.getTname() + " 生成代码文件");

            // 构建模板上下文
            Map<String, String> context = buildTemplateContext(table, fields, packageName);
            System.out.println("模板上下文构建完成，实体名: " + context.get("entityName"));

            // 生成实体类
            String entityTemplate = readTemplateFile("Admin.java");
            String entityCode = replaceTemplateVariables(entityTemplate, context);
            files.put("src/main/java/com/model/" + context.get("entityName") + ".java", entityCode);
            System.out.println("生成实体类: " + context.get("entityName") + ".java");

            // 生成控制器
            String controllerTemplate = readTemplateFile("AdminAction.java");
            String controllerCode = replaceTemplateVariables(controllerTemplate, context);
            files.put("src/main/java/com/controller/" + context.get("entityName") + "Action.java", controllerCode);
            System.out.println("生成控制器: " + context.get("entityName") + "Action.java");

            // 生成Mapper接口
            String mapperTemplate = readTemplateFile("AdminMapper.java");
            String mapperCode = replaceTemplateVariables(mapperTemplate, context);
            files.put("src/main/java/com/mapper/" + context.get("entityName") + "Mapper.java", mapperCode);
            System.out.println("生成Mapper接口: " + context.get("entityName") + "Mapper.java");

            // 生成Service接口
            String serviceTemplate = readTemplateFile("AdminService.java");
            String serviceCode = replaceTemplateVariables(serviceTemplate, context);
            files.put("src/main/java/com/service/" + context.get("entityName") + "Service.java", serviceCode);
            System.out.println("生成Service接口: " + context.get("entityName") + "Service.java");

            // 生成Service实现
            String serviceImplTemplate = readTemplateFile("AdminServiceImpl.java");
            String serviceImplCode = replaceTemplateVariables(serviceImplTemplate, context);
            files.put("src/main/java/com/service/impl/" + context.get("entityName") + "ServiceImpl.java", serviceImplCode);
            System.out.println("生成Service实现: " + context.get("entityName") + "ServiceImpl.java");

            // 生成Mapper XML
            String mapperXmlTemplate = readTemplateFile("adminMapper.xml");
            String mapperXmlCode = replaceTemplateVariables(mapperXmlTemplate, context);
            files.put("src/main/resources/com/mapper/" + context.get("entityNameLower") + "Mapper.xml", mapperXmlCode);
            System.out.println("生成Mapper XML: " + context.get("entityNameLower") + "Mapper.xml");

            System.out.println("表 " + table.getTname() + " 的代码文件生成完成，共 " + files.size() + " 个文件");

        } catch (Exception e) {
            System.err.println("为表 " + table.getTname() + " 生成代码文件时出错: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }

        return files;
    }

    /**
     * 读取模板文件内容（从代码模板目录）
     */
    private String readTemplateFile(String templateFileName) throws Exception {
        Path templatePath = Paths.get(TEMPLATE_DIR, templateFileName);
        if (!Files.exists(templatePath)) {
            throw new Exception("模板文件不存在: " + templatePath);
        }
        return new String(Files.readAllBytes(templatePath), StandardCharsets.UTF_8);
    }

    /**
     * 读取代码模板文件内容（从代码模板目录）
     */
    private String readCodeTemplateFile(String templateFileName) throws Exception {
        Path templatePath = Paths.get(TEMPLATE_DIR, templateFileName);
        if (!Files.exists(templatePath)) {
            throw new Exception("代码模板文件不存在: " + templatePath);
        }
        return new String(Files.readAllBytes(templatePath), StandardCharsets.UTF_8);
    }

    /**
     * 读取项目模板文件内容（从项目模板目录）
     */
    private String readProjectTemplateFile(String templateFileName) throws Exception {
        Path templatePath = Paths.get(PROJECT_ROOT, "moban", "项目模版", "springboot", "src", "main", "java", "com",
                "controller", templateFileName);
        if (!Files.exists(templatePath)) {
            throw new Exception("项目模板文件不存在: " + templatePath);
        }
        return new String(Files.readAllBytes(templatePath), StandardCharsets.UTF_8);
    }

    /**
     * 构建模板上下文变量
     */
    private Map<String, String> buildTemplateContext(Tables table, List<Mores> fields, String packageName) {
        return buildTemplateContext(table, fields, packageName, "mysql");
    }

    /**
     * 构建模板上下文变量（支持数据库类型）
     */
    private Map<String, String> buildTemplateContext(Tables table, List<Mores> fields, String packageName, String databaseType) {
        Map<String, String> context = new HashMap<>();

        // 基本信息
        String entityName = toPascalCase(table.getTname());
        String entityNameLower = toCamelCase(table.getTname());

        context.put("packageName", packageName);
        context.put("entityName", entityName);
        context.put("entityNameLower", entityNameLower);
        context.put("entityComment", table.getTword() != null ? table.getTword() : table.getTname());
        context.put("tableName", table.getTname());
        context.put("databaseType", databaseType != null ? databaseType : "mysql");

        // 生成字段相关代码
        context.put("fieldDeclarations", generateFieldDeclarations(fields));
        context.put("getterSetterMethods", generateGetterSetterMethods(fields));
        context.put("toStringFields", generateToStringFields(fields));

        // MyBatis相关
        context.put("namespace", packageName + ".mapper." + entityName + "Mapper");
        context.put("entityType", packageName + ".model." + entityName);
        context.put("primaryKey", getPrimaryKey(fields));
        context.put("primaryKeyProperty", getPrimaryKeyProperty(fields));
        context.put("dynamicColumns", generateDynamicQueryConditions(fields));
        context.put("insertColumns", generateInsertColumns(fields));
        context.put("insertValues", generateInsertValues(fields, getDatabaseTypeFromContext(context)));
        context.put("updateColumns", generateUpdateColumns(fields));

        // 主键参数类型
        context.put("primaryKeyParamType", getPrimaryKeyParamType(fields));

        // 字段映射代码
        context.put("fieldMapPuts", generateFieldMapPuts(fields, entityNameLower));

        // 其他代码生成
        String entityComment = table.getTword() != null ? table.getTword() : table.getTname();
        context.put("existsCheckCode", generateExistsCheckCode(fields, entityName, entityNameLower, entityComment));
        context.put("idParamCode", generateIdParamCode(fields));

        // 生成编辑器字段处理代码
        context.put("editorFieldsProcessing", generateEditorFieldsProcessing(fields, entityName, entityNameLower));

        // 移除登录和修改密码方法的生成，这些方法将在IndexAction.java中统一生成
        context.put("loginMethod", "");
        context.put("passwordMethod", "");

        // 生成注册和个人信息方法
        String registrationAndProfileMethods = generateTableRegistrationAndProfileMethods(table, fields);
        context.put("registrationAndProfileMethods", registrationAndProfileMethods);

        return context;
    }

    /**
     * 为单个表生成注册和个人信息方法
     */
    private String generateTableRegistrationAndProfileMethods(Tables table, List<Mores> fields) {
        StringBuilder methods = new StringBuilder();

        Map<String, Boolean> tableFunctions = parseTableFunctions(table.getTgn());
        String tableName = table.getTname();
        String tableComment = table.getTword() != null ? table.getTword() : table.getTname();
        String entityName = toPascalCase(tableName);
        String entityNameLower = toCamelCase(tableName);

        // 如果选中了后台注册，生成注册方法
        if (tableFunctions.getOrDefault("backendRegister", false)) {
            methods.append(generateRegistrationMethods(table, fields, entityName, entityNameLower, tableComment));
            methods.append("\n\n");
        }

        // 如果选中了后台个人信息，生成个人信息方法
        if (tableFunctions.getOrDefault("backendProfile", false)) {
            methods.append(generateProfileMethods(table, fields, entityName, entityNameLower, tableComment));
            methods.append("\n\n");
        }

        return methods.toString();
    }

    /**
     * 替换模板变量
     */
    private String replaceTemplateVariables(String template, Map<String, String> context) {
        String result = template;
        for (Map.Entry<String, String> entry : context.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            result = result.replace(placeholder, entry.getValue() != null ? entry.getValue() : "");
        }
        return result;
    }

    /**
     * 转换为PascalCase（首字母大写的驼峰命名）
     */
    private String toPascalCase(String str) {
        if (str == null || str.isEmpty())
            return str;
        return str.substring(0, 1).toUpperCase() + toCamelCase(str.substring(1));
    }

    /**
     * 转换为camelCase（首字母小写的驼峰命名）
     */
    private String toCamelCase(String str) {
        if (str == null || str.isEmpty())
            return str;

        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = false;

        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (c == '_' || c == '-') {
                capitalizeNext = true;
            } else if (capitalizeNext) {
                result.append(Character.toUpperCase(c));
                capitalizeNext = false;
            } else {
                result.append(Character.toLowerCase(c));
            }
        }

        return result.toString();
    }

    /**
     * 生成字段声明代码
     */
    private String generateFieldDeclarations(List<Mores> fields) {
        StringBuilder sb = new StringBuilder();
        for (Mores field : fields) {
            String javaType = mapToJavaType(field.getMotype());
            String fieldName = toCamelCase(field.getMoname());
            String comment = field.getMozname() != null ? field.getMozname() : field.getMoname();

            sb.append("\t/** ").append(comment).append(" */\n");
            sb.append("\tprivate ").append(javaType).append(" ").append(fieldName).append(";\n\n");
        }
        return sb.toString();
    }

    /**
     * 生成Getter和Setter方法
     */
    private String generateGetterSetterMethods(List<Mores> fields) {
        StringBuilder sb = new StringBuilder();
        for (Mores field : fields) {
            String javaType = mapToJavaType(field.getMotype());
            String fieldName = toCamelCase(field.getMoname());
            String methodName = toPascalCase(field.getMoname());

            // Getter方法
            sb.append("\tpublic ").append(javaType).append(" get").append(methodName).append("() {\n");
            sb.append("\t\treturn ").append(fieldName).append(";\n");
            sb.append("\t}\n\n");

            // Setter方法
            sb.append("\tpublic void set").append(methodName).append("(").append(javaType).append(" ").append(fieldName)
                    .append(") {\n");
            sb.append("\t\tthis.").append(fieldName).append(" = ").append(fieldName).append(";\n");
            sb.append("\t}\n\n");
        }
        return sb.toString();
    }

    /**
     * 生成toString方法中的字段
     */
    private String generateToStringFields(List<Mores> fields) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < fields.size(); i++) {
            Mores field = fields.get(i);
            String fieldName = toCamelCase(field.getMoname());

            if (i > 0)
                sb.append(" +\n\t\t\t\t");
            sb.append("\"").append(fieldName).append("=\" + ").append(fieldName);
            if (i < fields.size() - 1)
                sb.append(" + \", \" +");
        }
        return sb.toString();
    }

    /**
     * 数据库类型映射到Java类型
     */
    private String mapToJavaType(String dbType) {
        if (dbType == null)
            return "String";

        String type = dbType.toLowerCase();
        if (type.contains("int") || type.equals("integer")) {
            return "Integer";
        } else if (type.contains("bigint") || type.contains("long")) {
            return "Long";
        } else if (type.contains("decimal") || type.contains("numeric") || type.contains("double")) {
            return "Double";
        } else if (type.contains("float")) {
            return "Float";
        } else if (type.contains("date") || type.contains("time")) {
            return "Date";
        } else if (type.contains("bool")) {
            return "Boolean";
        } else {
            return "String";
        }
    }

    /**
     * 获取主键字段名
     */
    private String getPrimaryKey(List<Mores> fields) {
        // 通常第一个字段是主键，或者查找名为id的字段
        if (!fields.isEmpty()) {
            Mores firstField = fields.get(0);
            return firstField.getMoname();
        }
        return "id";
    }

    /**
     * 获取主键属性名
     */
    private String getPrimaryKeyProperty(List<Mores> fields) {
        String primaryKey = getPrimaryKey(fields);
        return toCamelCase(primaryKey);
    }

    /**
     * 获取主键参数类型
     */
    private String getPrimaryKeyParamType(List<Mores> fields) {
        if (!fields.isEmpty()) {
            Mores primaryField = fields.get(0);
            return mapToJavaType(primaryField.getMotype());
        }
        return "Integer";
    }

    /**
     * 生成字段映射代码
     */
    private String generateFieldMapPuts(List<Mores> fields, String entityNameLower) {
        StringBuilder sb = new StringBuilder();

        for (Mores field : fields) {
            String getterMethod = "get" + toPascalCase(field.getMoname()) + "()";

            sb.append("\t\t\tmap.put(\"").append(field.getMoname()).append("\", ")
              .append(entityNameLower).append(".").append(getterMethod).append(");\n");
        }

        // 添加ComData基类的通用字段
        sb.append("\t\t\tmap.put(\"sort\", ").append(entityNameLower).append(".getSort());\n");
        sb.append("\t\t\tmap.put(\"condition\", ").append(entityNameLower).append(".getCondition());\n");

        return sb.toString();
    }

    /**
     * 生成动态查询条件
     */
    private String generateDynamicQueryConditions(List<Mores> fields) {
        StringBuilder sb = new StringBuilder();
        for (Mores field : fields) {
            String fieldName = field.getMoname();
            String propertyName = toCamelCase(fieldName);
            String javaType = mapToJavaType(field.getMotype());

            // 根据字段类型生成不同的查询条件
            if ("String".equals(javaType)) {
                sb.append("\t\t<if test=\"").append(propertyName).append(" != null and ").append(propertyName).append(" != ''\">\n");
                sb.append("\t\t    and a.").append(fieldName).append(" = #{").append(propertyName).append("}\n");
                sb.append("\t\t</if>\n");
            } else {
                sb.append("\t\t<if test=\"").append(propertyName).append(" != null and ").append(propertyName).append(" != 0\">\n");
                sb.append("\t\t    and a.").append(fieldName).append(" = #{").append(propertyName).append("}\n");
                sb.append("\t\t</if>\n");
            }
        }
        return sb.toString();
    }

    /**
     * 生成动态查询列（用于SELECT子句）
     */
    private String generateDynamicColumns(List<Mores> fields) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < fields.size(); i++) {
            Mores field = fields.get(i);
            if (i > 0)
                sb.append(", ");
            sb.append("a.").append(field.getMoname());
        }
        return sb.toString();
    }

    /**
     * 生成插入列名
     */
    private String generateInsertColumns(List<Mores> fields) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < fields.size(); i++) {
            Mores field = fields.get(i);
            if (i > 0)
                sb.append(",");
            sb.append(field.getMoname());
        }
        return sb.toString();
    }

    /**
     * 生成插入值
     */
    private String generateInsertValues(List<Mores> fields) {
        return generateInsertValues(fields, "mysql"); // 默认使用MySQL
    }

    /**
     * 生成插入值（支持数据库类型）
     */
    private String generateInsertValues(List<Mores> fields, String databaseType) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < fields.size(); i++) {
            Mores field = fields.get(i);
            String propertyName = toCamelCase(field.getMoname());
            if (i > 0)
                sb.append(",");

            // 检查是否为自动当前时间字段
            if ("自动当前时间".equals(field.getMoflag())) {
                if ("sqlserver".equalsIgnoreCase(databaseType)) {
                    sb.append("getdate()");
                } else {
                    sb.append("now()"); // 默认使用MySQL的now()
                }
            } else {
                sb.append("#{").append(propertyName).append("}");
            }
        }
        return sb.toString();
    }

    /**
     * 从上下文中获取数据库类型
     */
    private String getDatabaseTypeFromContext(Map<String, String> context) {
        // 尝试从上下文中获取数据库类型，如果没有则默认为mysql
        String databaseType = context.get("databaseType");
        return databaseType != null ? databaseType : "mysql";
    }

    /**
     * 生成更新列
     */
    private String generateUpdateColumns(List<Mores> fields) {
        StringBuilder sb = new StringBuilder();
        for (Mores field : fields) {
            // 跳过主键字段
            if (field == fields.get(0))
                continue;

            String fieldName = field.getMoname();
            String propertyName = toCamelCase(fieldName);
            String javaType = mapToJavaType(field.getMotype());
            String controlType = field.getMoflag(); // 控件类型

            // 跳过自动当前时间字段（这些字段在更新时不应该被手动设置）
            if ("自动当前时间".equals(controlType)) {
                continue;
            }

            // 为每个字段添加空值判断
            sb.append("\t\t<if test=\"").append(propertyName).append(" != null");

            // 对于String类型，还要检查是否为空字符串
            if ("String".equals(javaType)) {
                sb.append(" and ").append(propertyName).append(" != ''");
            }

            sb.append("\">\n");
            sb.append("\t\t    ").append(fieldName).append(" = #{").append(propertyName).append("},\n");
            sb.append("\t\t</if>\n");
        }
        return sb.toString();
    }

    /**
     * 生成存在检查代码
     */
    private String generateExistsCheckCode(List<Mores> fields, String entityName, String entityNameLower, String entityComment) {
        if (fields == null || fields.isEmpty()) {
            return "";
        }

        // 收集需要存在验证的字段
        List<Mores> existsCheckFields = new ArrayList<>();
        for (Mores field : fields) {
            String by2 = field.getBy2(); // by2字段存储"存在"验证信息
            if (by2 != null && !by2.trim().isEmpty() && !"否".equals(by2.trim()) && !"0".equals(by2.trim())) {
                existsCheckFields.add(field);
            }
        }

        if (existsCheckFields.isEmpty()) {
            // 没有需要验证的字段，生成简单的添加逻辑
            StringBuilder simpleCode = new StringBuilder();
            simpleCode.append("\t\t").append(entityNameLower).append("Service.insert").append(entityName).append("(").append(entityNameLower).append("); // 添加").append(entityComment).append("\n");
            simpleCode.append("\t\treq.setAttribute(\"message\", \"操作成功\");\n");
            simpleCode.append("\t\treq.setAttribute(\"path\", \"").append(entityNameLower).append("ToAdd\");\n");
            return simpleCode.toString();
        }

        // 生成存在检查代码 - 为每个字段单独检查
        StringBuilder checkCode = new StringBuilder();

        // 为每个需要验证的字段生成单独的检查逻辑
        for (int i = 0; i < existsCheckFields.size(); i++) {
            Mores field = existsCheckFields.get(i);
            String fieldName = field.getMoname();
            String setterMethod = "set" + toPascalCase(fieldName);
            String getterMethod = "get" + toPascalCase(fieldName);
            String fieldComment = field.getMozname();
            if (fieldComment == null || fieldComment.trim().isEmpty()) {
                fieldComment = field.getMoname();
            }

            checkCode.append("\t\t").append(entityName).append(" check").append(entityName).append(i + 1).append(" = new ").append(entityName).append("();\n");
            checkCode.append("\t\tcheck").append(entityName).append(i + 1).append(".").append(setterMethod)
                    .append("(").append(entityNameLower).append(".").append(getterMethod).append("());\n\n");

            checkCode.append("\t\tList<").append(entityName).append("> checkList").append(i + 1).append(" = ")
                    .append(entityNameLower).append("Service.query").append(entityName).append("List(check")
                    .append(entityName).append(i + 1).append(", null);\n");

            checkCode.append("\t\tif (checkList").append(i + 1).append(" != null && checkList").append(i + 1).append(".size() > 0) {\n");
            checkCode.append("\t\t\treq.setAttribute(\"alert\", \"该").append(fieldComment).append("已存在，请重新输入\");\n");

            if (i == existsCheckFields.size() - 1) {
                // 最后一个字段的else分支包含插入逻辑
                checkCode.append("\t\t} else {\n");
                checkCode.append("\t\t\t").append(entityNameLower).append("Service.insert").append(entityName).append("(").append(entityNameLower).append("); // 添加").append(entityComment).append("\n");
                checkCode.append("\t\t\treq.setAttribute(\"message\", \"操作成功\");\n");
                checkCode.append("\t\t\treq.setAttribute(\"path\", \"").append(entityNameLower).append("ToAdd\");\n");
                checkCode.append("\t\t}");
            } else {
                // 中间字段的else分支继续下一个检查
                checkCode.append("\t\t} else {\n");
            }
        }

        // 为每个字段添加对应的右大括号（除了最后一个）
        for (int i = 0; i < existsCheckFields.size() - 1; i++) {
            checkCode.append("\n\t\t}");
        }

        return checkCode.toString();
    }

    /**
     * 生成编辑器字段处理代码
     */
    private String generateEditorFieldsProcessing(List<Mores> fields, String entityName, String entityNameLower) {
        if (fields == null || fields.isEmpty()) {
            return "";
        }

        // 检查是否有编辑器类型的字段
        boolean hasEditorFields = false;
        for (Mores field : fields) {
            String controlType = field.getMoflag(); // 控件类型
            if ("编辑器".equals(controlType)) {
                hasEditorFields = true;
                break;
            }
        }

        if (!hasEditorFields) {
            return ""; // 没有编辑器字段，不需要处理
        }

        // 生成编辑器字段处理代码
        StringBuilder processingCode = new StringBuilder();
        processingCode.append("\n\t\t//遍历\n");
        processingCode.append("\t\tfor (").append(entityName).append(" ").append(entityNameLower).append(" : ").append(entityNameLower).append("List) {\n");

        // 为每个编辑器字段生成处理代码
        for (Mores field : fields) {
            String controlType = field.getMoflag(); // 控件类型
            if ("编辑器".equals(controlType)) {
                String fieldName = field.getMoname();
                String getterMethod = "get" + toPascalCase(fieldName);
                String setterMethod = "set" + toPascalCase(fieldName);

                processingCode.append("\t\t\t").append(entityNameLower).append(".").append(setterMethod)
                    .append("(removeHTML.Html2Text(").append(entityNameLower).append(".").append(getterMethod).append("()));\n");
            }
        }

        processingCode.append("\t\t}\n");
        return processingCode.toString();
    }

    /**
     * 生成ID参数代码
     */
    private String generateIdParamCode(List<Mores> fields) {
        if (!fields.isEmpty()) {
            Mores primaryField = fields.get(0);
            String javaType = mapToJavaType(primaryField.getMotype());

            if ("Integer".equals(javaType)) {
                return "\t\tint id = Integer.parseInt(req.getParameter(\"id\"));";
            } else if ("Long".equals(javaType)) {
                return "\t\tlong id = Long.parseLong(req.getParameter(\"id\"));";
            } else {
                return "\t\tString id = req.getParameter(\"id\");";
            }
        }
        return "\t\tint id = Integer.parseInt(req.getParameter(\"id\"));";
    }

    /**
     * 解析表功能配置
     */
    private Map<String, Boolean> parseTableFunctions(String tgn) {
        Map<String, Boolean> functions = new HashMap<>();

        // 默认值
        functions.put("backendLogin", false);
        functions.put("backendPassword", false);
        functions.put("backendRegister", false);
        functions.put("backendProfile", false);
        functions.put("backendAdd", true);
        functions.put("backendEdit", true);
        functions.put("backendDelete", true);
        functions.put("backendDetail", true);
        functions.put("backendList", true);

        if (tgn != null && !tgn.trim().isEmpty()) {
            try {
                // 检查是否是JSON格式
                if (tgn.trim().startsWith("{") && tgn.trim().endsWith("}")) {
                    // 解析JSON格式的配置
                    functions.put("backendLogin", extractBooleanFromJson(tgn, "backendLogin"));
                    functions.put("backendPassword", extractBooleanFromJson(tgn, "backendPassword"));
                    functions.put("backendRegister", extractBooleanFromJson(tgn, "backendRegister"));
                    functions.put("backendProfile", extractBooleanFromJson(tgn, "backendProfile"));
                    functions.put("backendAdd", extractBooleanFromJson(tgn, "backendAdd"));
                    functions.put("backendEdit", extractBooleanFromJson(tgn, "backendEdit"));
                    functions.put("backendDelete", extractBooleanFromJson(tgn, "backendDelete"));
                    functions.put("backendDetail", extractBooleanFromJson(tgn, "backendDetail"));
                    functions.put("backendList", extractBooleanFromJson(tgn, "backendList"));
                } else {
                    // 兼容旧的字符串包含检查方式
                    if (tgn.contains("\"backendLogin\":true")) {
                        functions.put("backendLogin", true);
                    }
                    if (tgn.contains("\"backendPassword\":true")) {
                        functions.put("backendPassword", true);
                    }
                    if (tgn.contains("\"backendRegister\":true")) {
                        functions.put("backendRegister", true);
                    }
                    if (tgn.contains("\"backendProfile\":true")) {
                        functions.put("backendProfile", true);
                    }
                    if (tgn.contains("\"backendAdd\":false")) {
                        functions.put("backendAdd", false);
                    }
                    if (tgn.contains("\"backendEdit\":false")) {
                        functions.put("backendEdit", false);
                    }
                    if (tgn.contains("\"backendDelete\":false")) {
                        functions.put("backendDelete", false);
                    }
                    if (tgn.contains("\"backendDetail\":false")) {
                        functions.put("backendDetail", false);
                    }
                    if (tgn.contains("\"backendList\":false")) {
                        functions.put("backendList", false);
                    }
                }
            } catch (Exception e) {
                System.err.println("解析表功能配置失败: " + e.getMessage());
            }
        }

        return functions;
    }

    /**
     * 从JSON字符串中提取布尔值
     */
    private boolean extractBooleanFromJson(String json, String key) {
        try {
            String pattern = "\"" + key + "\"\\s*:\\s*(true|false)";
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher m = p.matcher(json);
            if (m.find()) {
                return Boolean.parseBoolean(m.group(1));
            }
        } catch (Exception e) {
            System.err.println("提取JSON字段失败: " + key + ", " + e.getMessage());
        }
        return false; // 默认值
    }

    /**
     * 查找用户名字段
     */
    private Mores findUsernameField(List<Mores> fields) {
        // 优先查找中文字段名包含"用户名"、"登录名"等的字段
        for (Mores field : fields) {
            String chineseName = field.getMozname();
            if (chineseName != null) {
                if (chineseName.contains("用户名") || chineseName.contains("登录名") ||
                        chineseName.contains("账号") || chineseName.contains("用户账号")) {
                    return field;
                }
            }
        }

        // 然后查找英文字段名
        for (Mores field : fields) {
            String englishName = field.getMoname();
            if (englishName != null) {
                String name = englishName.toLowerCase();
                if (name.contains("lname") || name.contains("username") ||
                        name.contains("loginname") || name.contains("uname") ||
                        name.contains("account")) {
                    return field;
                }
            }
        }

        return null;
    }

    /**
     * 查找密码字段
     */
    private Mores findPasswordField(List<Mores> fields) {
        // 优先查找中文字段名包含"密码"的字段
        for (Mores field : fields) {
            String chineseName = field.getMozname();
            if (chineseName != null && chineseName.contains("密码")) {
                return field;
            }
        }

        // 然后查找英文字段名
        for (Mores field : fields) {
            String englishName = field.getMoname();
            if (englishName != null) {
                String name = englishName.toLowerCase();
                if (name.contains("password") || name.contains("pwd") ||
                        name.contains("loginpassword")) {
                    return field;
                }
            }
        }

        return null;
    }

    /**
     * 生成统一的登录方法
     */
    private String generateUnifiedLoginMethods(Map<String, Integer> projectFunctions, List<Tables> tablesList) {
        StringBuilder allMethods = new StringBuilder();

        int loginTableCount = projectFunctions.getOrDefault("backendLogin", 0);

        // 生成登录方法
        if (loginTableCount > 0) {
            if (loginTableCount == 1) {
                // 单表登录
                allMethods.append(generateSingleTableLoginMethod(tablesList));
            } else {
                // 多表登录
                allMethods.append(generateMultiTableLoginMethod(tablesList));
            }
        }

        // 注册和个人信息方法将在各自的表控制器中生成，不在IndexAction中生成

        return allMethods.toString();
    }

    /**
     * 生成注册和个人信息方法
     */
    private String generateRegistrationAndProfileMethods(List<Tables> tablesList) {
        StringBuilder methods = new StringBuilder();

        for (Tables table : tablesList) {
            Map<String, Boolean> tableFunctions = parseTableFunctions(table.getTgn());
            String tableName = table.getTname();
            String tableComment = table.getTword() != null ? table.getTword() : table.getTname();
            String entityName = toPascalCase(tableName);
            String entityNameLower = toCamelCase(tableName);

            // 获取表的字段信息
            List<Mores> fieldsList = getTableFields(table.getTid());

            // 如果选中了后台注册，生成注册方法
            if (tableFunctions.getOrDefault("backendRegister", false)) {
                methods.append(generateRegistrationMethods(table, fieldsList, entityName, entityNameLower, tableComment));
                methods.append("\n\n");
            }

            // 如果选中了后台个人信息，生成个人信息方法
            if (tableFunctions.getOrDefault("backendProfile", false)) {
                methods.append(generateProfileMethods(table, fieldsList, entityName, entityNameLower, tableComment));
                methods.append("\n\n");
            }
        }

        return methods.toString();
    }

    /**
     * 生成注册方法
     */
    private String generateRegistrationMethods(Tables table, List<Mores> fieldsList, String entityName, String entityNameLower, String tableComment) {
        StringBuilder methods = new StringBuilder();

        // 跳转到注册页面方法
        methods.append("\t// 跳转到注册页面\n");
        methods.append("\t@RequestMapping(value=\"/to").append(entityNameLower).append("Reg\")\n");
        methods.append("\tpublic String to").append(entityNameLower).append("Reg(HttpServletRequest req)throws Exception{\n");
        methods.append("\t    \n");
        methods.append("\t\treturn \"/").append(entityNameLower).append("_Reg\";\n");
        methods.append("\t}\n\n");

        // 注册方法
        methods.append("\t// ").append(tableComment).append("注册\n");
        methods.append("\t@RequestMapping(value = \"/").append(entityNameLower).append("Reg\")\n");
        methods.append("\tpublic String ").append(entityNameLower).append("Reg(").append(entityName).append(" ").append(entityNameLower).append(", HttpServletRequest request) throws Exception {\n\n");

        // 获取用户名字段（第一个非主键字段）
        String usernameField = getUsernameFieldName(fieldsList);
        String usernameGetter = "get" + toPascalCase(usernameField);
        String usernameSetter = "set" + toPascalCase(usernameField);

        methods.append("\t\t").append(entityName).append(" te = new ").append(entityName).append("();\n");
        methods.append("\t\tte.").append(usernameSetter).append("(").append(entityNameLower).append(".").append(usernameGetter).append("());\n");
        methods.append("\t\tString pass2 = request.getParameter(\"pwd2\");\n");
        methods.append("\t\tList<").append(entityName).append("> list = ").append(entityNameLower).append("Service.query").append(entityName).append("List(te, null);\n");
        methods.append("\t\tif (list != null && list.size() > 0) {\n");
        methods.append("\t\t\trequest.setAttribute(\"alert\", \"该用户名已存在，请重新输入\");\n");
        methods.append("\t\t\t} else {\n\n");

        // 获取密码字段
        String passwordField = getPasswordFieldName(fieldsList);
        String passwordGetter = "get" + toPascalCase(passwordField);

        methods.append("\t\t\tif (pass2.equals(").append(entityNameLower).append(".").append(passwordGetter).append("())) {\n");
        methods.append("\t\t\t\t").append(entityNameLower).append("Service.insert").append(entityName).append("(").append(entityNameLower).append(");\n");
        methods.append("\t\t\t\trequest.setAttribute(\"alert2\", \"注册成功，请登录\");\n");
        methods.append("\t\t\t\trequest.setAttribute(\"path\", \"qtologin\");\n");
        methods.append("\t\t\t\t} else {\n");
        methods.append("\t\t\t\trequest.setAttribute(\"alert\", \"两次密码输入不一致，请重新输入\");\n");
        methods.append("\t\t\t}\n");
        methods.append("\t\t}\n");
        methods.append("\t\treturn \"common/succeed\";\n");
        methods.append("\t}");

        return methods.toString();
    }

    /**
     * 生成个人信息方法
     */
    private String generateProfileMethods(Tables table, List<Mores> fieldsList, String entityName, String entityNameLower, String tableComment) {
        StringBuilder methods = new StringBuilder();

        // 获取第一个字段的类型来判断ID类型
        String idType = getIdType(table.getTid());
        String idCastType = idType.indexOf("varchar") >= 0 ? "String" : "Integer";
        String idFieldName = fieldsList.get(0).getMoname();
        String idGetter = "get" + toPascalCase(idFieldName);

        // 跳转到修改个人信息页面方法
        methods.append("\t// 跳转到修改个人信息页面\n");
        methods.append("\t@RequestMapping(value=\"/to").append(entityNameLower).append("Info\")\n");
        methods.append("\tpublic String to").append(entityNameLower).append("Info(HttpServletRequest req)throws Exception{\n");
        methods.append("\t\t").append(idCastType).append(" id = (").append(idCastType).append(")req.getSession().getAttribute(\"id\");\n");
        methods.append("\t\t").append(entityName).append(" ").append(entityNameLower).append(" = ").append(entityNameLower).append("Service.query").append(entityName).append("ById(id);\n");
        methods.append("\t\treq.setAttribute(\"item\", ").append(entityNameLower).append(");\n");
        methods.append("\t\t\n");
        methods.append("\t\treturn \"/admin/").append(entityNameLower).append("/").append(entityNameLower).append("_Info\";\n");
        methods.append("\t}\n\n");

        // 修改个人信息方法
        methods.append("\t// 修改个人信息\n");
        methods.append("\t@RequestMapping(value=\"/").append(entityNameLower).append("Info\")\n");
        methods.append("\tpublic String ").append(entityNameLower).append("Info(").append(entityName).append(" ").append(entityNameLower).append(",HttpServletRequest req)throws Exception{\n");
        methods.append("\t    \t\t\t\n");
        methods.append("\t\t").append(entityNameLower).append("Service.update").append(entityName).append("(").append(entityNameLower).append(");\n");
        methods.append("\t    \t\t\n");
        methods.append("\t\treq.setAttribute(\"message\",\"操作成功\");\n");
        methods.append("\t\treq.setAttribute(\"path\",\"to").append(entityNameLower).append("Info\");\n");
        methods.append("\t\treturn \"common/succeed\";\n");
        methods.append("\t}");

        return methods.toString();
    }

    /**
     * 获取表的字段信息
     */
    private List<Mores> getTableFields(Integer tableId) {
        try {
            Mores queryMores = new Mores();
            queryMores.setTid(tableId);
            return moresService.queryMoresList(queryMores, null);
        } catch (Exception e) {
            System.err.println("获取表字段失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 获取用户名字段名称
     */
    private String getUsernameFieldName(List<Mores> fieldsList) {
        if (fieldsList != null && fieldsList.size() > 1) {
            // 第一个字段通常是主键，第二个字段通常是用户名
            return fieldsList.get(1).getMoname();
        }
        return "lname"; // 默认值
    }

    /**
     * 获取密码字段名称
     */
    private String getPasswordFieldName(List<Mores> fieldsList) {
        if (fieldsList != null) {
            for (Mores field : fieldsList) {
                String chineseName = field.getMozname();
                if (chineseName != null && chineseName.contains("密码")) {
                    return field.getMoname();
                }
            }
            // 如果没找到包含"密码"的字段，返回第三个字段（通常是密码字段）
            if (fieldsList.size() > 2) {
                return fieldsList.get(2).getMoname();
            }
        }
        return "password"; // 默认值
    }

    /**
     * 生成单表登录方法
     */
    private String generateSingleTableLoginMethod(List<Tables> tablesList) {
        // 找到有登录功能的表
        Tables loginTable = null;
        for (Tables table : tablesList) {
            Map<String, Boolean> tableFunctions = parseTableFunctions(table.getTgn());
            if (tableFunctions.getOrDefault("backendLogin", false)) {
                loginTable = table;
                break;
            }
        }

        if (loginTable == null) {
            return "";
        }

        String entityName = toPascalCase(loginTable.getTname());
        String entityNameLower = toCamelCase(loginTable.getTname());
        String tableComment = loginTable.getTword() != null ? loginTable.getTword() : loginTable.getTname();

        // 获取项目配置的session信息
        Map<String, String> sessionConfig = getSessionConfig(loginTable.getTid());

        // 获取表的字段信息
        Map<String, String> fieldsInfo = getTableFieldsInfo(loginTable.getTid());

        // 获取第一个字段的类型来判断ID类型
        String idType = getIdType(loginTable.getTid());
        String idCastType = idType.indexOf("varchar") >= 0 ? "String" : "Integer";

        return String.format(
                "\t// 登录\n" +
                        "\t@RequestMapping(value = \"/adminLogin\", produces = \"text/html;charset=UTF-8\")\n" +
                        "\tpublic String adminLogin(HttpServletRequest req) throws Exception {\n\n" +
                        "\t\treq.setCharacterEncoding(\"UTF-8\");\n\n" +
                        "\t\t%s %s = new %s();\n" +
                        "\t\t%s.%s(req.getParameter(\"txtUserName\"));\n" +
                        "\t\t%s.%s(req.getParameter(\"txtPassWord\"));\n\n" +
                        "\t\tList<%s> %sList = %sService.query%sList(%s, null);\n" +
                        "\t\tif (%sList != null && %sList.size() > 0) {\n" +
                        "\t\t\t%s %s2 = (%s) %sList.get(0);\n" +
                        "\t\t\tHttpSession session = req.getSession();\n" +
                        "\t\t\tsession.setAttribute(\"%s\", %s2.%s());\n" +
                        "\t\t\tsession.setAttribute(\"%s\", %s2.%s());\n" +
                        "\t\t\tsession.setAttribute(\"%s\", \"%s\");\n" +
                        "\t\t\treq.setAttribute(\"message\", \"登录成功\");\n" +
                        "\t\t\treq.setAttribute(\"path\", \"adminindex\"); // 跳转到后台首页\n" +
                        "\t\t} else {\n" +
                        "\t\t\treq.setAttribute(\"alert\", \"用户名或密码错误\");\n" +
                        "\t\t\treq.setAttribute(\"path\", \"/login.jsp\");\n" +
                        "\t\t}\n\n" +
                        "\t\treturn \"common/succeed\";\n" +
                        "\t}\n\n" +
                        "\t// 修改密码\n" +
                        "\t@RequestMapping(value = \"/adminPass\")\n" +
                        "\tpublic String adminPass(HttpServletRequest req) throws Exception {\n" +
                        "\t\tHttpSession session = req.getSession();\n\n" +
                        "\t\tString pwd1 = req.getParameter(\"txt_pwd\");\n" +
                        "\t\tString pwd2 = req.getParameter(\"txt_pwd2\");\n" +
                        "\t\tString pwd3 = req.getParameter(\"txt_pwd3\");\n\n" +
                        "\t\tif (!pwd2.equals(pwd3)) {\n" +
                        "\t\t\treq.setAttribute(\"alert\", \"新密码和确认密码不一致，请重新输入\");\n" +
                        "\t\t} else {\n\n" +
                        "\t\t\tString role = (String) session.getAttribute(\"%s\");// 用户角色\n" +
                        "\t\t\t%s Aid = (%s) session.getAttribute(\"%s\");// 用户ID\n\n" +
                        "\t\t\t%s %s = new %s();\n" +
                        "\t\t\t%s.%s(Aid);\n" +
                        "\t\t\t%s.%s(pwd1);\n\n" +
                        "\t\t\tint temp = %sService.getCount(%s); // 查询数据库中是否存在该用户\n" +
                        "\t\t\tif (temp > 0) {\n" +
                        "\t\t\t\t%s.%s(pwd2); // 设置新密码\n\n" +
                        "\t\t\t\t%sService.update%s(%s); // 更新密码\n\n" +
                        "\t\t\t\treq.setAttribute(\"message\", \"修改成功\");\n" +
                        "\t\t\t\treq.setAttribute(\"path\", \"/toadminpass\");\n\n" +
                        "\t\t\t} else {\n" +
                        "\t\t\t\treq.setAttribute(\"alert\", \"原密码错误，请重新输入\");\n\n" +
                        "\t\t\t}\n\n" +
                        "\t\t}\n" +
                        "\t\treturn \"common/succeed\";\n" +
                        "\t}",
                entityName, entityNameLower, entityName,
                entityNameLower, fieldsInfo.get("lnameSetterMethod"),
                entityNameLower, fieldsInfo.get("passwordSetterMethod"),
                entityName, entityNameLower, entityNameLower, entityName, entityNameLower,
                entityNameLower, entityNameLower,
                entityName, entityNameLower, entityName, entityNameLower,
                sessionConfig.get("idField"), entityNameLower, fieldsInfo.get("idGetterMethod"),
                sessionConfig.get("lnameField"), entityNameLower, fieldsInfo.get("lnameGetterMethod"),
                sessionConfig.get("roleField"), tableComment,
                sessionConfig.get("roleField"),
                idCastType, idCastType, sessionConfig.get("idField"),
                entityName, entityNameLower, entityName,
                entityNameLower, fieldsInfo.get("idSetterMethod"),
                entityNameLower, fieldsInfo.get("passwordSetterMethod"),
                entityNameLower, entityNameLower,
                entityNameLower, fieldsInfo.get("passwordSetterMethod"),
                entityNameLower, entityName, entityNameLower);
    }

    /**
     * 生成Service声明
     */
    private String generateServiceDeclarations(List<Tables> tablesList) {
        StringBuilder declarations = new StringBuilder();

        for (Tables table : tablesList) {
            Map<String, Boolean> tableFunctions = parseTableFunctions(table.getTgn());
            if (tableFunctions.getOrDefault("backendLogin", false)) {
                String entityName = toPascalCase(table.getTname());
                declarations.append("import com.service.").append(entityName).append("Service;\n");
            }
        }

        return declarations.toString();
    }

    /**
     * 生成Service自动注入
     */
    private String generateServiceAutowired(List<Tables> tablesList) {
        StringBuilder autowired = new StringBuilder();

        for (Tables table : tablesList) {
            Map<String, Boolean> tableFunctions = parseTableFunctions(table.getTgn());
            if (tableFunctions.getOrDefault("backendLogin", false)) {
                String entityName = toPascalCase(table.getTname());
                String entityNameLower = toCamelCase(table.getTname());
                autowired.append("\t@Autowired private ").append(entityName).append("Service ").append(entityNameLower)
                        .append("Service;\n");
            }
        }

        return autowired.toString();
    }

    /**
     * 生成多表登录方法
     */
    private String generateMultiTableLoginMethod(List<Tables> tablesList) {
        StringBuilder loginMethod = new StringBuilder();
        StringBuilder passwordMethod = new StringBuilder();

        // 生成登录方法开始
        loginMethod.append("\t// 登录\n")
                .append("\t@RequestMapping(value = \"/adminLogin\", produces = \"text/html;charset=UTF-8\")\n")
                .append("\tpublic String adminLogin(HttpServletRequest req) throws Exception {\n\n")
                .append("\t\treq.setCharacterEncoding(\"UTF-8\");\n")
                .append("\t\tString role = req.getParameter(\"role\");\n\n");

        // 生成修改密码方法开始
        passwordMethod.append("\t// 修改密码\n")
                .append("\t@RequestMapping(value = \"/adminPass\")\n")
                .append("\tpublic String adminPass(HttpServletRequest req) throws Exception {\n")
                .append("\t\tHttpSession session = req.getSession();\n\n")
                .append("\t\tString pwd1 = req.getParameter(\"txt_pwd\");\n")
                .append("\t\tString pwd2 = req.getParameter(\"txt_pwd2\");\n")
                .append("\t\tString pwd3 = req.getParameter(\"txt_pwd3\");\n\n")
                .append("\t\tif (!pwd2.equals(pwd3)) {\n")
                .append("\t\t\treq.setAttribute(\"alert\", \"新密码和确认密码不一致，请重新输入\");\n")
                .append("\t\t} else {\n\n")
                .append("\t\t\tString role = (String) session.getAttribute(\"role\");// 用户角色\n");

        boolean isFirst = true;
        for (Tables table : tablesList) {
            Map<String, Boolean> tableFunctions = parseTableFunctions(table.getTgn());
            if (tableFunctions.getOrDefault("backendLogin", false)) {
                String entityName = toPascalCase(table.getTname());
                String entityNameLower = toCamelCase(table.getTname());
                String tableComment = table.getTword() != null ? table.getTword() : table.getTname();

                // 获取项目配置的session信息
                Map<String, String> sessionConfig = getSessionConfig(table.getTid());

                // 获取表的字段信息
                Map<String, String> fieldsInfo = getTableFieldsInfo(table.getTid());

                // 获取第一个字段的类型来判断ID类型
                String idType = getIdType(table.getTid());
                String idCastType = idType.indexOf("varchar") >= 0 ? "String" : "Integer";

                // 添加登录逻辑
                if (isFirst) {
                    loginMethod.append("\t\tif (role.equals(\"").append(tableComment).append("\")) {\n");
                    passwordMethod.append("\t\t\tif (role.equals(\"").append(tableComment).append("\")) {\n");
                } else {
                    loginMethod.append("\t\t} else if (role.equals(\"").append(tableComment).append("\")) {\n");
                    passwordMethod.append("\t\t\t} else if (role.equals(\"").append(tableComment).append("\")) {\n");
                }

                // 登录方法内容
                loginMethod.append("\t\t\t").append(entityName).append(" ").append(entityNameLower).append(" = new ")
                        .append(entityName).append("();\n")
                        .append("\t\t\t").append(entityNameLower).append(".")
                        .append(fieldsInfo.get("lnameSetterMethod")).append("(req.getParameter(\"txtUserName\"));\n")
                        .append("\t\t\t").append(entityNameLower).append(".")
                        .append(fieldsInfo.get("passwordSetterMethod")).append("(req.getParameter(\"txtPassWord\"));\n\n")
                        .append("\t\t\tList<").append(entityName).append("> ").append(entityNameLower).append("List = ")
                        .append(entityNameLower).append("Service.query").append(entityName).append("List(")
                        .append(entityNameLower).append(", null);\n")
                        .append("\t\t\tif (").append(entityNameLower).append("List != null && ").append(entityNameLower)
                        .append("List.size() > 0) {\n")
                        .append("\t\t\t\t").append(entityName).append(" ").append(entityNameLower).append("2 = (")
                        .append(entityName).append(") ").append(entityNameLower).append("List.get(0);\n")
                        .append("\t\t\t\tHttpSession session = req.getSession();\n")
                        .append("\t\t\t\tsession.setAttribute(\"").append(sessionConfig.get("idField")).append("\", ")
                        .append(entityNameLower).append("2.").append(fieldsInfo.get("idGetterMethod")).append("());\n")
                        .append("\t\t\t\tsession.setAttribute(\"").append(sessionConfig.get("lnameField"))
                        .append("\", ").append(entityNameLower).append("2.").append(fieldsInfo.get("lnameGetterMethod")).append("());\n")
                        .append("\t\t\t\tsession.setAttribute(\"").append(sessionConfig.get("roleField"))
                        .append("\", \"").append(tableComment).append("\");\n")
                        .append("\t\t\t\treq.setAttribute(\"message\", \"登录成功\");\n")
                        .append("\t\t\t\treq.setAttribute(\"path\", \"adminindex\"); // 跳转到后台首页\n")
                        .append("\t\t\t} else {\n")
                        .append("\t\t\t\treq.setAttribute(\"alert\", \"用户名或密码错误\");\n")
                        .append("\t\t\t\treq.setAttribute(\"path\", \"/login.jsp\");\n")
                        .append("\t\t\t}\n");

                // 修改密码方法内容
                passwordMethod.append("\t\t\t\t").append(idCastType).append(" Aid = (").append(idCastType)
                        .append(") session.getAttribute(\"").append(sessionConfig.get("idField"))
                        .append("\");// 用户ID\n\n")
                        .append("\t\t\t\t").append(entityName).append(" ").append(entityNameLower).append(" = new ")
                        .append(entityName).append("();\n")
                        .append("\t\t\t\t").append(entityNameLower).append(".").append(fieldsInfo.get("idSetterMethod")).append("(Aid);\n")
                        .append("\t\t\t\t").append(entityNameLower).append(".").append(fieldsInfo.get("passwordSetterMethod")).append("(pwd1);\n\n")
                        .append("\t\t\t\tint temp = ").append(entityNameLower).append("Service.getCount(")
                        .append(entityNameLower).append("); // 查询数据库中是否存在该用户\n")
                        .append("\t\t\t\tif (temp > 0) {\n")
                        .append("\t\t\t\t\t").append(entityNameLower).append(".").append(fieldsInfo.get("passwordSetterMethod")).append("(pwd2); // 设置新密码\n\n")
                        .append("\t\t\t\t\t").append(entityNameLower).append("Service.update").append(entityName)
                        .append("(").append(entityNameLower).append("); // 更新密码\n\n")
                        .append("\t\t\t\t\treq.setAttribute(\"message\", \"修改成功\");\n")
                        .append("\t\t\t\t\treq.setAttribute(\"path\", \"/toadminpass\");\n\n")
                        .append("\t\t\t\t} else {\n")
                        .append("\t\t\t\t\treq.setAttribute(\"alert\", \"原密码错误，请重新输入\");\n\n")
                        .append("\t\t\t\t}\n");

                isFirst = false;
            }
        }

        // 结束登录方法
        loginMethod.append("\t\t}\n\n")
                .append("\t\treturn \"common/succeed\";\n")
                .append("\t}\n\n");

        // 结束修改密码方法
        passwordMethod.append("\t\t\t}\n\n")
                .append("\t\t}\n")
                .append("\t\treturn \"common/succeed\";\n")
                .append("\t}");

        return loginMethod.toString() + passwordMethod.toString();
    }

    /**
     * 获取项目的session配置信息
     */
    private Map<String, String> getSessionConfig(Integer tableId) {
        Map<String, String> config = new HashMap<>();

        try {
            // 通过表ID获取项目ID
            Tables table = tablesService.queryTablesById(tableId);
            if (table != null && table.getPid() != null) {
                Projects project = projectsService.queryProjectsById(table.getPid());
                if (project != null && project.getBy3() != null && !project.getBy3().trim().isEmpty()) {
                    // 解析by3字段，格式可能是: "id,lname,role" 或类似格式
                    String[] sessionFields = project.getBy3().split(",");
                    if (sessionFields.length >= 3) {
                        config.put("idField", sessionFields[0].trim());
                        config.put("lnameField", sessionFields[1].trim());
                        config.put("roleField", sessionFields[2].trim());
                    } else {
                        // 使用默认值
                        config.put("idField", "id");
                        config.put("lnameField", "lname");
                        config.put("roleField", "role");
                    }
                } else {
                    // 使用默认值
                    config.put("idField", "id");
                    config.put("lnameField", "lname");
                    config.put("roleField", "role");
                }
            }
        } catch (Exception e) {
            // 出错时使用默认值
            config.put("idField", "id");
            config.put("lnameField", "lname");
            config.put("roleField", "role");
        }

        return config;
    }

    /**
     * 获取表的第一个字段类型来判断ID类型
     */
    private String getIdType(Integer tableId) {
        try {
            List<Mores> fields = moresService.queryMoresList(new Mores() {
                {
                    setTid(tableId);
                }
            }, null);
            if (fields != null && !fields.isEmpty()) {
                return fields.get(0).getMotype();
            }
        } catch (Exception e) {
            // 出错时返回默认类型
        }
        return "int"; // 默认为int类型
    }

    /**
     * 获取表的字段信息用于登录方法生成
     */
    private Map<String, String> getTableFieldsInfo(Integer tableId) {
        Map<String, String> fieldsInfo = new HashMap<>();

        try {
            List<Mores> fields = moresService.queryMoresList(new Mores() {{ setTid(tableId); }}, null);
            if (fields != null && !fields.isEmpty()) {
                // 第一个字段作为ID字段
                if (fields.size() > 0) {
                    Mores firstField = fields.get(0);
                    fieldsInfo.put("idFieldName", toCamelCase(firstField.getMoname()));
                    fieldsInfo.put("idFieldType", mapToJavaType(firstField.getMotype()));
                    fieldsInfo.put("idGetterMethod", "get" + toPascalCase(firstField.getMoname()));
                    fieldsInfo.put("idSetterMethod", "set" + toPascalCase(firstField.getMoname()));

                    // 根据第一个字段的类型决定用户名和密码字段的选择
                    String firstFieldType = firstField.getMotype();
                    boolean isFirstFieldInt = firstFieldType != null && firstFieldType.toLowerCase().contains("int");

                    if (isFirstFieldInt) {
                        // 如果第1个字段是int类型，就取表的第2个和第3个字段作为用户名和密码
                        if (fields.size() > 1) {
                            Mores secondField = fields.get(1);
                            fieldsInfo.put("lnameFieldName", toCamelCase(secondField.getMoname()));
                            fieldsInfo.put("lnameGetterMethod", "get" + toPascalCase(secondField.getMoname()));
                            fieldsInfo.put("lnameSetterMethod", "set" + toPascalCase(secondField.getMoname()));
                        } else {
                            // 默认使用lname
                            fieldsInfo.put("lnameFieldName", "lname");
                            fieldsInfo.put("lnameGetterMethod", "getLname");
                            fieldsInfo.put("lnameSetterMethod", "setLname");
                        }
                    } else {
                        // 如果第1个字段是varchar类型，就取表的第1个和第2个字段作为用户名和密码
                        fieldsInfo.put("lnameFieldName", toCamelCase(firstField.getMoname()));
                        fieldsInfo.put("lnameGetterMethod", "get" + toPascalCase(firstField.getMoname()));
                        fieldsInfo.put("lnameSetterMethod", "set" + toPascalCase(firstField.getMoname()));
                    }
                }

                // 根据第一个字段的类型决定密码字段的选择
                String passwordFieldName = "loginpassword"; // 默认值
                String passwordGetterMethod = "getLoginpassword";
                String passwordSetterMethod = "setLoginpassword";

                if (fields.size() > 0) {
                    Mores firstField = fields.get(0);
                    String firstFieldType = firstField.getMotype();
                    boolean isFirstFieldInt = firstFieldType != null && firstFieldType.toLowerCase().contains("int");

                    if (isFirstFieldInt) {
                        // 如果第1个字段是int类型，密码字段是第3个字段
                        if (fields.size() > 2) {
                            Mores thirdField = fields.get(2);
                            passwordFieldName = toCamelCase(thirdField.getMoname());
                            passwordGetterMethod = "get" + toPascalCase(thirdField.getMoname());
                            passwordSetterMethod = "set" + toPascalCase(thirdField.getMoname());
                        }
                    } else {
                        // 如果第1个字段是varchar类型，密码字段是第2个字段
                        if (fields.size() > 1) {
                            Mores secondField = fields.get(1);
                            passwordFieldName = toCamelCase(secondField.getMoname());
                            passwordGetterMethod = "get" + toPascalCase(secondField.getMoname());
                            passwordSetterMethod = "set" + toPascalCase(secondField.getMoname());
                        }
                    }
                }

                // 如果上面的逻辑没有找到合适的字段，尝试查找中文名称包含"密码"的字段
                if (passwordFieldName.equals("loginpassword")) {
                    for (Mores field : fields) {
                        if (field.getMozname() != null && field.getMozname().contains("密码")) {
                            passwordFieldName = toCamelCase(field.getMoname());
                            passwordGetterMethod = "get" + toPascalCase(field.getMoname());
                            passwordSetterMethod = "set" + toPascalCase(field.getMoname());
                            break;
                        }
                    }
                }

                fieldsInfo.put("passwordFieldName", passwordFieldName);
                fieldsInfo.put("passwordGetterMethod", passwordGetterMethod);
                fieldsInfo.put("passwordSetterMethod", passwordSetterMethod);
            }
        } catch (Exception e) {
            // 出错时使用默认值
            fieldsInfo.put("idFieldName", "aid");
            fieldsInfo.put("idFieldType", "Integer");
            fieldsInfo.put("idGetterMethod", "getAid");
            fieldsInfo.put("idSetterMethod", "setAid");
            fieldsInfo.put("lnameFieldName", "lname");
            fieldsInfo.put("lnameGetterMethod", "getLname");
            fieldsInfo.put("lnameSetterMethod", "setLname");
            fieldsInfo.put("passwordFieldName", "loginpassword");
            fieldsInfo.put("passwordGetterMethod", "getLoginpassword");
            fieldsInfo.put("passwordSetterMethod", "setLoginpassword");
        }

        return fieldsInfo;
    }

    /**
     * 生成后台登录表的中文名称单选框
     */
    private String generateBackendLoginRadioButtons(Integer projectId) {
        StringBuilder radioButtons = new StringBuilder();

        try {
            if (projectId == null) {
                return "";
            }

            // 获取项目下所有表
            Tables queryTable = new Tables();
            queryTable.setPid(projectId);
            List<Tables> tablesList = tablesService.queryTablesList(queryTable, null);

            if (tablesList == null || tablesList.isEmpty()) {
                return "";
            }

            boolean isFirst = true;
            for (Tables table : tablesList) {
                // 检查表是否有后台登录功能
                Map<String, Boolean> tableFunctions = parseTableFunctions(table.getTgn());
                if (tableFunctions.getOrDefault("backendLogin", false)) {
                    String tableComment = table.getTword() != null ? table.getTword() : table.getTname();

                    // 生成单选框HTML
                    if (isFirst) {
                        radioButtons.append("<input type=\"radio\" name=\"role\" value=\"")
                                   .append(tableComment)
                                   .append("\" checked=\"checked\" />")
                                   .append(tableComment)
                                   .append("\n");
                        isFirst = false;
                    } else {
                        radioButtons.append("<input type=\"radio\" name=\"role\" value=\"")
                                   .append(tableComment)
                                   .append("\" />")
                                   .append(tableComment)
                                   .append("\n");
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("生成后台登录单选框失败: " + e.getMessage());
        }

        return radioButtons.toString();
    }

    /**
     * 生成动态菜单
     */
    private String generateDynamicMenu(Integer projectId, Map<String, String> templateData) {
        StringBuilder menuContent = new StringBuilder();

        try {
            if (projectId == null) {
                return "";
            }

            // 获取项目下所有表
            Tables queryTable = new Tables();
            queryTable.setPid(projectId);
            List<Tables> tablesList = tablesService.queryTablesList(queryTable, null);

            if (tablesList == null || tablesList.isEmpty()) {
                return "";
            }

            // 按菜单显示顺序排序（by2字段）
            tablesList.sort((t1, t2) -> {
                int order1 = 1; // 默认顺序
                int order2 = 1; // 默认顺序

                try {
                    if (t1.getBy2() != null && !t1.getBy2().trim().isEmpty()) {
                        order1 = Integer.parseInt(t1.getBy2().trim());
                    }
                } catch (NumberFormatException e) {
                    order1 = 1;
                }

                try {
                    if (t2.getBy2() != null && !t2.getBy2().trim().isEmpty()) {
                        order2 = Integer.parseInt(t2.getBy2().trim());
                    }
                } catch (NumberFormatException e) {
                    order2 = 1;
                }

                return Integer.compare(order1, order2);
            });

            // 获取菜单模板 - 从small表的memo2和memo3字段获取
            String menuTemplate = templateData.getOrDefault("memo2", ""); // memo2 - 一级菜单模板
            String submenuTemplate = templateData.getOrDefault("memo3", ""); // memo3 - 子菜单模板

            // 如果没有模板，使用默认模板
            if (menuTemplate.isEmpty()) {
                menuTemplate = "<div class=\"menu-item\">\n" +
                              "    <a href=\"#\" class=\"menu-link\" data-bs-toggle=\"collapse\" data-bs-target=\"#$tableNameLower$Menu\" aria-expanded=\"false\">\n" +
                              "        <i class=\"bi bi-table menu-icon\"></i>\n" +
                              "        <span class=\"menu-text\">$lanmu$</span>\n" +
                              "        <i class=\"bi bi-chevron-down menu-arrow\"></i>\n" +
                              "    </a>\n" +
                              "    <div class=\"submenu\" id=\"$tableNameLower$Menu\">\n" +
                              "       $navmenu$\n" +
                              "    </div>\n" +
                              "</div>";
            }

            if (submenuTemplate.isEmpty()) {
                submenuTemplate = "<a href=\"$url$\" class=\"menu-link\" target=\"main_iframe\">\n" +
                                 "    <span class=\"menu-text\">$mch$</span>\n" +
                                 "</a>";
            }

            // 为每个表生成菜单
            for (Tables table : tablesList) {
                Map<String, Boolean> tableFunctions = parseTableFunctions(table.getTgn());
                String tableComment = table.getTword() != null ? table.getTword() : table.getTname();
                String tableNameLower = toCamelCase(table.getTname());

                // 检查表是否有任何后台功能
                boolean hasBackendFunctions = tableFunctions.getOrDefault("backendAdd", false) ||
                                            tableFunctions.getOrDefault("backendDelete", false) ||
                                            tableFunctions.getOrDefault("backendList", false);

                if (hasBackendFunctions) {
                    // 生成子菜单项
                    StringBuilder submenuItems = new StringBuilder();

                    // 列表功能 - 对应 adminList 方法
                    if (tableFunctions.getOrDefault("backendList", false)) {
                        String listSubmenu = submenuTemplate
                            .replace("$url$", "/" + tableNameLower + "List2")
                            .replace("$mch$", tableComment + "列表");
                        submenuItems.append("        ").append(listSubmenu).append("\n");
                    }

                    // 添加功能 - 对应 adminToAdd 方法
                    if (tableFunctions.getOrDefault("backendAdd", false)) {
                        String addSubmenu = submenuTemplate
                            .replace("$url$", "/" + tableNameLower + "ToAdd")
                            .replace("$mch$", "添加" + tableComment);
                        submenuItems.append("        ").append(addSubmenu).append("\n");
                    }

                    // 管理功能（原删除功能） - 对应 adminList2 方法
                    if (tableFunctions.getOrDefault("backendDelete", false)) {
                        String manageSubmenu = submenuTemplate
                            .replace("$url$", "/" + tableNameLower + "List")
                            .replace("$mch$","管理" + tableComment  );
                        submenuItems.append("        ").append(manageSubmenu).append("\n");
                    }

                    // 生成一级菜单
                    if (submenuItems.length() > 0) {
                        String tableMenu = menuTemplate
                            .replace("$lanmu$", tableComment + "管理")
                            .replace("$navmenu$", submenuItems.toString().trim())
                            .replace("$tableNameLower$", tableNameLower);

                        menuContent.append(tableMenu).append("\n\n");
                    }
                }
            }

            // 添加统计报表菜单（如果项目启用了统计图表）- 倒数第2个位置
            String statisticsMenu = generateStatisticsMenu(projectId, menuTemplate, submenuTemplate);
            if (!statisticsMenu.isEmpty()) {
                menuContent.append(statisticsMenu).append("\n\n");
            }

            // 添加系统管理菜单 - 最后一个位置
            String systemMenu = generateSystemManagementMenu(projectId, tablesList, menuTemplate, submenuTemplate);
            if (!systemMenu.isEmpty()) {
                menuContent.append(systemMenu).append("\n\n");
            }

        } catch (Exception e) {
            System.err.println("生成动态菜单失败: " + e.getMessage());
        }

        return menuContent.toString();
    }

    /**
     * 替换用户显示变量为Thymeleaf表达式
     */
    private String replaceUserDisplayVariables(String content, Integer projectId) {
        try {
            if (projectId == null) {
                // 使用默认的session属性名
                content = content.replace("$yonghuming$", "<span th:text=\"${session.lname}\"></span>");
                content = content.replace("$role$", "<span th:text=\"${session.role}\"></span>");
                return content;
            }

            // 获取项目的session配置信息
            Map<String, String> sessionConfig = getSessionConfig(projectId);

            // 替换用户名显示
            String usernameExpression = "<span th:text=\"${session." + sessionConfig.get("lnameField") + "}\"></span>";
            content = content.replace("$$yonghuming$$", usernameExpression);
            content = content.replace("$yonghuming$", usernameExpression);

            // 替换身份显示
            String roleExpression = "<span th:text=\"${session." + sessionConfig.get("roleField") + "}\"></span>";
            content = content.replace("$$shenfen$$", roleExpression);
            content = content.replace("$role$", roleExpression);

        } catch (Exception e) {
            System.err.println("替换用户显示变量失败: " + e.getMessage());
            // 出错时使用默认值
            content = content.replace("$$yonghuming$$", "<span th:text=\"${session.lname}\"></span>");
            content = content.replace("$$shenfen$$", "<span th:text=\"${session.role}\"></span>");
        }

        return content;
    }

    /**
     * 生成统计报表菜单
     */
    private String generateStatisticsMenu(Integer projectId, String menuTemplate, String submenuTemplate) {
        try {
            if (projectId == null) {
                return "";
            }

            // 获取项目信息，检查是否启用了统计图表
            Projects project = projectsService.queryProjectsById(projectId);
            if (project == null || project.getBy4() == null || !project.getBy4().equals("是")) {
                return ""; // 如果统计图表不是"是"，则不显示统计报表菜单
            }

            // 生成统计报表子菜单
            StringBuilder submenuItems = new StringBuilder();

            // 商品分类销售统计
            String categoryReportSubmenu = submenuTemplate
                .replace("$url$", "/categoryReport")
                .replace("$mch$", "商品分类销售统计");
            submenuItems.append("        ").append(categoryReportSubmenu).append("\n");

            // 商品销售排行
            String goodsRankingSubmenu = submenuTemplate
                .replace("$url$", "/goodsRanking")
                .replace("$mch$", "商品销售排行");
            submenuItems.append("        ").append(goodsRankingSubmenu).append("\n");

            // 日销售统计
            String dailySalesSubmenu = submenuTemplate
                .replace("$url$", "/dailySales")
                .replace("$mch$", "日销售统计");
            submenuItems.append("        ").append(dailySalesSubmenu).append("\n");

            // 月销售统计
            String monthlySalesSubmenu = submenuTemplate
                .replace("$url$", "/monthlySales")
                .replace("$mch$", "月销售统计");
            submenuItems.append("        ").append(monthlySalesSubmenu).append("\n");

            // 生成统计报表一级菜单
            String statisticsMenu = menuTemplate
                .replace("$lanmu$", "统计报表")
                .replace("$navmenu$", submenuItems.toString().trim())
                .replace("$tableNameLower$", "statistics");

            return statisticsMenu;

        } catch (Exception e) {
            System.err.println("生成统计报表菜单失败: " + e.getMessage());
            return "";
        }
    }

    /**
     * 生成系统管理菜单
     */
    private String generateSystemManagementMenu(Integer projectId, List<Tables> tablesList, String menuTemplate, String submenuTemplate) {
        try {
            if (projectId == null || tablesList == null) {
                return "";
            }

            // 查找管理员表（有登录功能的表）
            Tables adminTable = null;
            for (Tables table : tablesList) {
                Map<String, Boolean> tableFunctions = parseTableFunctions(table.getTgn());
                if (tableFunctions.getOrDefault("backendLogin", false)) {
                    adminTable = table;
                    break; // 找到第一个有登录功能的表作为管理员表
                }
            }

            if (adminTable == null) {
                return ""; // 没有管理员表，不显示系统管理菜单
            }

            // 检查管理员表是否有身份字段
            boolean hasRoleField = checkAdminTableHasRoleField(adminTable.getTid());

            // 生成系统管理子菜单
            StringBuilder submenuItems = new StringBuilder();

            // 如果有身份字段，显示管理员相关菜单
            if (hasRoleField) {
                String tableNameLower = toCamelCase(adminTable.getTname());

                // 添加管理员
                String addAdminSubmenu = submenuTemplate
                    .replace("$url$", "/" + tableNameLower + "ToAdd")
                    .replace("$mch$", "添加管理员");
                submenuItems.append("        ").append(addAdminSubmenu).append("\n");

                // 管理管理员
                String manageAdminSubmenu = submenuTemplate
                    .replace("$url$", "/" + tableNameLower + "List")
                    .replace("$mch$", "管理管理员");
                submenuItems.append("        ").append(manageAdminSubmenu).append("\n");
            }

            // 修改密码（总是显示）
            String changePasswordSubmenu = submenuTemplate
                .replace("$url$", "/toadminpass")
                .replace("$mch$", "修改密码");
            submenuItems.append("        ").append(changePasswordSubmenu).append("\n");

            // 生成系统管理一级菜单
            String systemMenu = menuTemplate
                .replace("$lanmu$", "系统管理")
                .replace("$navmenu$", submenuItems.toString().trim())
                .replace("$tableNameLower$", "system");

            return systemMenu;

        } catch (Exception e) {
            System.err.println("生成系统管理菜单失败: " + e.getMessage());
            return "";
        }
    }

    /**
     * 检查管理员表是否有身份字段
     */
    private boolean checkAdminTableHasRoleField(Integer tableId) {
        try {
            List<Mores> fields = moresService.queryMoresList(new Mores() {{ setTid(tableId); }}, null);
            if (fields != null) {
                for (Mores field : fields) {
                    String chineseName = field.getMozname();
                    if (chineseName != null && (chineseName.contains("身份") || chineseName.contains("角色") || chineseName.contains("权限"))) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("检查管理员表身份字段失败: " + e.getMessage());
        }
        return false;
    }

    /**
     * 生成CRUD页面
     */
    private Map<String, String> generateCrudPages(String templateId, Integer projectId, Path backendTemplatePath) {
        Map<String, String> files = new HashMap<>();

        try {
            if (projectId == null) {
                return files;
            }

            // 读取temp.html模板文件
            Path tempTemplatePath = backendTemplatePath.resolve("temp.html");
            if (!Files.exists(tempTemplatePath)) {
                System.err.println("temp.html模板文件不存在: " + tempTemplatePath);
                return files;
            }

            String tempTemplate = new String(Files.readAllBytes(tempTemplatePath), StandardCharsets.UTF_8);

            // 获取项目下所有表
            Tables queryTable = new Tables();
            queryTable.setPid(projectId);
            List<Tables> tablesList = tablesService.queryTablesList(queryTable, null);

            if (tablesList == null || tablesList.isEmpty()) {
                return files;
            }

            // 为每个表生成CRUD页面
            for (Tables table : tablesList) {
                Map<String, Boolean> tableFunctions = parseTableFunctions(table.getTgn());
                String tableName = table.getTname();
                String tableComment = table.getTword() != null ? table.getTword() : table.getTname();

                // 检查各种功能并生成对应页面

                // 管理页面命名为：表名称_Manage（数据列表页面）
                String managePage = crudPageGeneratorService.generateSingleCrudPage(tempTemplate, table, "manage", "管理", tableFunctions);
                files.put("src/main/resources/templates/admin/" + tableName + "/" + tableName + "_Manage.html", managePage);
                System.out.println("生成管理页面: " + tableName + "_Manage.html");

                // 列表页面命名为：表名称_Manage2（与管理页面内容相同）
                if (tableFunctions.getOrDefault("backendList", false)) {
                    String listPage = crudPageGeneratorService.generateSingleCrudPage(tempTemplate, table, "list", "列表", tableFunctions);
                    files.put("src/main/resources/templates/admin/" + tableName + "/" + tableName + "_Manage2.html", listPage);
                    System.out.println("生成列表页面: " + tableName + "_Manage2.html");
                }

                // 如果选中后台添加，生成单独的添加页面
                if (tableFunctions.getOrDefault("backendAdd", false)) {
                    String addPage = crudPageGeneratorService.generateSingleCrudPage(tempTemplate, table, "add", "添加", tableFunctions);
                    files.put("src/main/resources/templates/admin/" + tableName + "/" + tableName + "_Add.html", addPage);
                    System.out.println("生成添加页面: " + tableName + "_Add.html");
                }

                // 编辑页面
                if (tableFunctions.getOrDefault("backendEdit", false)) {
                    String editPage = crudPageGeneratorService.generateSingleCrudPage(tempTemplate, table, "edit", "编辑", tableFunctions);
                    files.put("src/main/resources/templates/admin/" + tableName + "/" + tableName + "_Edit.html", editPage);
                    System.out.println("生成编辑页面: " + tableName + "_Edit.html");
                }

                // 详情页面
                if (tableFunctions.getOrDefault("backendDetail", false)) {
                    String detailPage = crudPageGeneratorService.generateSingleCrudPage(tempTemplate, table, "detail", "详情", tableFunctions);
                    files.put("src/main/resources/templates/admin/" + tableName + "/" + tableName + "_Detail.html", detailPage);
                    System.out.println("生成详情页面: " + tableName + "_Detail.html");
                }

                // 不生成删除页面 - 根据用户要求移除
                // if (tableFunctions.getOrDefault("backendDelete", false)) {
                //     String deletePage = crudPageGeneratorService.generateSingleCrudPage(tempTemplate, table, "delete", "删除", tableFunctions);
                //     files.put("src/main/resources/templates/admin/" + tableName + "/" + tableName + "_Delete.html", deletePage);
                //     System.out.println("生成删除页面: " + tableName + "_Delete.html");
                // }
            }

            // 生成修改密码页面
            Map<String, String> passwordChangeFiles = crudPageGeneratorService.generatePasswordChangePageFiles();
            files.putAll(passwordChangeFiles);

            // 生成注册和个人信息页面
            Map<String, String> registrationAndPersonalInfoFiles = crudPageGeneratorService.generateRegistrationAndPersonalInfoPages(projectId);
            files.putAll(registrationAndPersonalInfoFiles);

        } catch (Exception e) {
            System.err.println("生成CRUD页面失败: " + e.getMessage());
            e.printStackTrace();
        }

        return files;
    }

}
