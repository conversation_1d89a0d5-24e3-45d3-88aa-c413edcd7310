<!-- Bootstrap CSS -->
<link href="/admin/css/bootstrap.min.css" rel="stylesheet">
<!-- Bootstrap Icons -->
<link href="/admin/css/bootstrap-icons.css" rel="stylesheet">


<!-- jQuery -->
<script src="/admin/js/jquery-3.3.1.min.js"></script>
<!-- Bootstrap JS -->
<script src="/admin/js/bootstrap.bundle.js"></script>


<!-- 美化样式 -->
<style>
    /* 全局样式 */
    body {
        background-color: #f8f9fa;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    /* 容器样式 */
    .container-fluid {
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
    }

    /* 卡片样式 */
    .card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
    }

    .card:hover {
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-bottom: none;
        border-radius: 12px 12px 0 0 !important;
        font-weight: 500;
        padding: 0.8rem 1.2rem;
    }

    .card-header h5 {
        margin: 0;
        font-size: 14px;
        font-weight: 500;
    }

    .card-body {
        padding: 1.5rem;
    }

    /* 表单样式 */
    .form-group {
        margin-bottom: 1rem;
    }

    .form-label,
    .col-form-label {
        font-weight: normal;
        color: #495057;
        margin-bottom: 0.3rem;
        font-size: 13px;
        text-align: right;
    }

    .form-control,
    .form-select {
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 0.5rem 0.8rem;
        font-size: 13px;
        transition: all 0.3s ease;
        width: 280px;
        max-width: 100%;
    }

    .form-control:focus,
    .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        transform: translateY(-1px);
    }

    .form-control.is-invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }

    .invalid-feedback {
        display: block;
        font-size: 0.875rem;
        color: #dc3545;
        margin-top: 0.25rem;
    }

    /* 必填字段标识 */
    .required {
        color: #dc3545;
        font-weight: bold;
    }

    /* 按钮样式 */
    .btn {
        border-radius: 6px;
        font-weight: 400;
        padding: 0.5rem 1rem;
        font-size: 13px;
        transition: all 0.3s ease;
        border: none;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
    }

    /* 表单按钮组对齐 */
    .form-group .btn-group,
    .form-group .d-flex,
    .row .col-sm-offset-2,
    .row .offset-sm-2 {
        display: flex;
        gap: 0.5rem;
    }

    /* 表单中的按钮容器对齐 */
    .card-body .btn:first-of-type {
        margin-left: 0;
    }

    /* 针对Bootstrap网格系统的按钮对齐 */
    .row .col-sm-10 .btn,
    .row .col-md-9 .btn,
    .row .col-lg-8 .btn {
        margin-right: 0.5rem;
    }

    /* 表单按钮区域样式 */
    .form-buttons {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
        align-items: center;
    }

    /* 当按钮在表单行中时的对齐 */
    .form-group .col-sm-offset-2 .btn,
    .form-group .offset-sm-2 .btn {
        margin-right: 0.5rem;
    }

    .form-group .col-sm-offset-2 .btn:last-child,
    .form-group .offset-sm-2 .btn:last-child {
        margin-right: 0;
    }

    /* 通用表单按钮对齐样式 */
    .card-body>.btn:not(:last-child),
    .card-body .btn+.btn {
        margin-right: 0.5rem;
    }

    /* 确保按钮组在表单中正确对齐 */
    .form-group:last-child,
    .row:last-child {
        margin-bottom: 0;
    }

    /* 表单底部按钮区域 */
    .form-actions,
    .form-footer {
        margin-top: 1.5rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    /* 当按钮在div容器中时的对齐 */
    div>.btn:not(:last-child) {
        margin-right: 0.5rem;
    }

    /* 特定的按钮对齐 - 针对常见的表单布局 */
    .text-center .btn:not(:last-child),
    .d-flex .btn:not(:last-child),
    .btn-group .btn:not(:last-child) {
        margin-right: 0.5rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .btn-success {
        background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    }

    .btn-danger {
        background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    }

    .btn-warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    .btn-info {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .btn-secondary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        opacity: 0.8;
    }

    .btn-sm {
        padding: 0.3rem 0.6rem;
        font-size: 12px;
        border-radius: 5px;
    }

    /* 表格样式 */
    .table {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .table th {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: none;
        font-weight: 500;
        color: #495057;
        padding: 0.8rem;
        font-size: 13px;
        text-align: center;
    }

    .table td {
        padding: 0.8rem;
        border-color: #f1f3f4;
        vertical-align: middle;
        text-align: center;
        font-weight: normal;
        font-size: 13px;
    }

    /* 详情页面内容样式 */
    .card-body p,
    .card-body span,
    .card-body div:not(.btn):not(.form-control):not(.alert) {
        font-weight: normal;
    }

    /* 详情页面标签和值的样式 */
    .card-body .row .col-sm-2,
    .card-body .row .col-md-2,
    .card-body .row .col-lg-2 {
        font-weight: normal;
        color: #6c757d;
    }

    .card-body .row .col-sm-10,
    .card-body .row .col-md-10,
    .card-body .row .col-lg-10 {
        font-weight: normal;
        color: #495057;
    }

    /* 详情页面文本内容 */
    .detail-content,
    .info-content,
    .view-content {
        font-weight: normal;
        color: #495057;
        font-size: 13px;
    }

    /* 详情页面所有文本元素 */
    .card-body *:not(.btn):not(.badge):not(.alert):not(h1):not(h2):not(h3):not(h4):not(h5):not(h6) {
        font-weight: normal !important;
    }

    /* 详情页面特定元素 */
    .card-body .form-control-plaintext {
        font-weight: normal;
        color: #495057;
        font-size: 13px;
        padding: 0.5rem 0;
    }

    /* 覆盖可能的加粗样式 */
    .card-body strong,
    .card-body b {
        font-weight: normal !important;
    }

    /* 详情页面定义列表样式 */
    .card-body dl,
    .card-body dt,
    .card-body dd {
        font-weight: normal;
        margin-bottom: 0.5rem;
    }

    .card-body dt {
        color: #6c757d;
    }

    .card-body dd {
        color: #495057;
        margin-left: 0;
    }

    /* 详情页面列表样式 */
    .card-body ul,
    .card-body ol,
    .card-body li {
        font-weight: normal;
    }

    /* 详情页面表格样式（如果在卡片内） */
    .card-body .table td,
    .card-body .table th {
        font-weight: normal;
    }

    /* 详情页面输入框readonly状态 */
    .card-body .form-control[readonly] {
        font-weight: normal;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(102, 126, 234, 0.08);
        transition: all 0.2s ease;
    }

    /* 分页样式 */
    .pagination {
        justify-content: center;
        margin-top: 2rem;
    }

    .pagination .page-link {
        border: 1px solid #e9ecef;
        border-radius: 6px;
        margin: 0 0.2rem;
        padding: 0.4rem 0.6rem;
        color: #667eea;
        font-weight: 400;
        font-size: 13px;
        transition: all 0.3s ease;
    }

    .pagination .page-link:hover {
        background-color: #667eea;
        border-color: #667eea;
        color: white;
        transform: translateY(-1px);
    }

    .pagination .page-item.active .page-link {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
    }

    /* 警告框样式 */
    .alert {
        border: none;
        border-radius: 10px;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
    }

    .alert-success {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        color: #155724;
    }

    .alert-danger {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
    }

    .alert-warning {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        color: #856404;
    }

    .alert-info {
        background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
        color: #0c5460;
    }

    /* Toast样式 */
    .toast {
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    /* 搜索表单样式 */
    .search-form {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        margin-bottom: 1.5rem;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .container-fluid {
            padding: 10px;
        }

        .card-body {
            padding: 1rem;
        }

        .table-responsive {
            font-size: 12px;
        }

        .btn {
            padding: 0.4rem 0.8rem;
            font-size: 12px;
        }

        .form-control,
        .form-select {
            width: 100%;
            padding: 0.4rem 0.6rem;
        }

        /* 移动端按钮对齐 */
        .form-buttons {
            flex-direction: column;
            align-items: stretch;
        }

        .form-buttons .btn {
            margin-bottom: 0.5rem;
            width: 100%;
        }
    }

    /* 加载动画 */
    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
    }

    /* 表单验证动画 */
    .form-control.is-invalid {
        animation: shake 0.5s ease-in-out;
    }

    @keyframes shake {

        0%,
        100% {
            transform: translateX(0);
        }

        25% {
            transform: translateX(-5px);
        }

        75% {
            transform: translateX(5px);
        }
    }
</style>

<!-- 通用JavaScript方法 -->
<script>
    // 全局变量
    window.CommonUtils = {
        // Bootstrap Toast提示函数
        showToast: function (message, type, duration) {
            type = type || 'success';
            duration = duration || 3000;

            var toastClass = '';
            var iconClass = '';
            var title = '';

            switch (type) {
                case 'success':
                    toastClass = 'bg-success';
                    iconClass = 'bi bi-check-circle-fill';
                    title = '成功';
                    break;
                case 'error':
                case 'danger':
                    toastClass = 'bg-danger';
                    iconClass = 'bi bi-exclamation-circle-fill';
                    title = '错误';
                    break;
                case 'warning':
                    toastClass = 'bg-warning';
                    iconClass = 'bi bi-exclamation-triangle-fill';
                    title = '警告';
                    break;
                case 'info':
                    toastClass = 'bg-info';
                    iconClass = 'bi bi-info-circle-fill';
                    title = '提示';
                    break;
                default:
                    toastClass = 'bg-primary';
                    iconClass = 'bi bi-info-circle-fill';
                    title = '提示';
            }

            var toastId = 'toast-' + Date.now();
            var toastHtml =
                '<div id="' + toastId + '" class="toast ' + toastClass + ' text-white" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="' + duration + '">' +
                '<div class="toast-header ' + toastClass + ' text-white border-0">' +
                '<i class="' + iconClass + ' me-2"></i>' +
                '<strong class="me-auto">' + title + '</strong>' +
                '<button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>' +
                '</div>' +
                '<div class="toast-body">' + message + '</div>' +
                '</div>';

            // 创建toast容器（如果不存在）
            if ($('#toast-container').length === 0) {
                $('body').append('<div id="toast-container" class="position-fixed top-0 end-0 p-3" style="z-index: 1055;"></div>');
            }

            // 添加toast并显示
            $('#toast-container').append(toastHtml);
            var toastElement = new bootstrap.Toast(document.getElementById(toastId));
            toastElement.show();

            // 自动移除DOM元素
            setTimeout(function () {
                $('#' + toastId).remove();
            }, duration + 500);
        },

        // 确认删除对话框
        confirmDelete: function (message, callback) {
            message = message || '确定要删除这条记录吗？此操作不可恢复！';

            // 创建Bootstrap Modal
            var modalId = 'deleteModal-' + Date.now();
            var modalHtml =
                '<div class="modal fade" id="' + modalId + '" tabindex="-1" aria-hidden="true">' +
                '<div class="modal-dialog modal-dialog-centered">' +
                '<div class="modal-content">' +
                '<div class="modal-header bg-danger text-white">' +
                '<h5 class="modal-title" style="font-weight: normal; font-size: 16px;">' +
                '<i class="bi bi-exclamation-triangle-fill me-2"></i>确认删除' +
                '</h5>' +
                '<button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>' +
                '</div>' +
                '<div class="modal-body">' +
                '<div class="text-center">' +
                '<i class="bi bi-trash3-fill text-danger" style="font-size: 3rem; margin-bottom: 1rem;"></i>' +
                '<p style="font-size: 14px; font-weight: normal;">' + message + '</p>' +
                '</div>' +
                '</div>' +
                '<div class="modal-footer">' +
                '<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">' +
                '<i class="bi bi-x-lg me-1"></i>取消' +
                '</button>' +
                '<button type="button" class="btn btn-danger" id="confirmDeleteBtn">' +
                '<i class="bi bi-trash3 me-1"></i>确认删除' +
                '</button>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>';

            // 添加modal到页面
            $('body').append(modalHtml);
            var modal = new bootstrap.Modal(document.getElementById(modalId));

            // 绑定确认按钮事件
            $('#' + modalId + ' #confirmDeleteBtn').on('click', function () {
                modal.hide();
                if (typeof callback === 'function') {
                    callback();
                }
            });

            // 清理DOM
            $('#' + modalId).on('hidden.bs.modal', function () {
                $(this).remove();
            });

            modal.show();
        },

        // 表单验证函数
        validateForm: function (formSelector) {
            var isValid = true;
            var $form = $(formSelector);

            // 清除之前的验证状态
            $form.find('.form-control, .form-select').removeClass('is-invalid');
            $form.find('.invalid-feedback').remove();

            // 验证必填字段
            $form.find('[required]').each(function () {
                var $field = $(this);
                var value = $field.val();
                var fieldName = $field.closest('.form-group').find('label').text().replace('*', '').trim();

                if (!value || value.trim() === '') {
                    $field.addClass('is-invalid');
                    $field.after('<div class="invalid-feedback">请输入' + fieldName + '</div>');
                    isValid = false;
                }
            });

            // 验证邮箱格式
            $form.find('input[type="email"]').each(function () {
                var $field = $(this);
                var value = $field.val();

                if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                    $field.addClass('is-invalid');
                    $field.after('<div class="invalid-feedback">请输入正确的邮箱格式</div>');
                    isValid = false;
                }
            });

            // 验证数字格式
            $form.find('input[type="number"]').each(function () {
                var $field = $(this);
                var value = $field.val();

                if (value && isNaN(value)) {
                    $field.addClass('is-invalid');
                    $field.after('<div class="invalid-feedback">请输入有效的数字</div>');
                    isValid = false;
                }
            });

            if (!isValid) {
                CommonUtils.showToast('请检查表单输入', 'error');
                // 滚动到第一个错误字段
                var firstError = $form.find('.is-invalid').first();
                if (firstError.length) {
                    $('html, body').animate({
                        scrollTop: firstError.offset().top - 100
                    }, 500);
                }
            }

            return isValid;
        },

        // AJAX表单提交
        submitForm: function (formSelector, url, successCallback, errorCallback) {
            var $form = $(formSelector);
            var $submitBtn = $form.find('button[type="submit"]');

            // 表单验证
            if (!CommonUtils.validateForm(formSelector)) {
                return false;
            }

            // 显示加载状态
            var originalText = $submitBtn.html();
            $submitBtn.prop('disabled', true).html(
                '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>提交中...'
            );

            // 提交表单
            $.ajax({
                url: url,
                type: 'POST',
                data: $form.serialize(),
                success: function (response) {
                    $submitBtn.prop('disabled', false).html(originalText);

                    // 检查响应是否为JSON格式
                    try {
                        var jsonResponse = typeof response === 'string' ? JSON.parse(response) : response;

                        // 根据Response类的code值处理不同情况
                        if (jsonResponse.code === 200) {
                            // 成功情况 (Response.success())
                            CommonUtils.showToast(jsonResponse.msg || '操作成功', 'success');
                            if (typeof successCallback === 'function') {
                                successCallback(jsonResponse);
                            }
                        } else if (jsonResponse.code === 201) {
                            // 业务逻辑错误 (如账号已存在)
                            CommonUtils.showToast(jsonResponse.msg || '操作失败', 'warning');
                            if (typeof errorCallback === 'function') {
                                errorCallback(jsonResponse);
                            }
                        } else if (jsonResponse.code === 500) {
                            // 服务器错误 (Response.error())
                            CommonUtils.showToast(jsonResponse.msg || '服务器错误', 'error');
                            if (typeof errorCallback === 'function') {
                                errorCallback(jsonResponse);
                            }
                        } else if (jsonResponse.code && jsonResponse.code !== 200) {
                            // 其他错误状态码
                            CommonUtils.showToast(jsonResponse.msg || '操作失败', 'error');
                            if (typeof errorCallback === 'function') {
                                errorCallback(jsonResponse);
                            }
                        } else {
                            // 兼容旧格式 - 检查success字段
                            if (jsonResponse.success || jsonResponse.success === true) {
                                CommonUtils.showToast(jsonResponse.msg || jsonResponse.message || '操作成功', 'success');
                                if (typeof successCallback === 'function') {
                                    successCallback(jsonResponse);
                                }
                            } else {
                                CommonUtils.showToast(jsonResponse.msg || jsonResponse.message || '操作失败', 'error');
                                if (typeof errorCallback === 'function') {
                                    errorCallback(jsonResponse);
                                }
                            }
                        }
                    } catch (e) {
                        // 如果不是JSON格式，检查响应内容
                        if (typeof response === 'string') {
                            if (response.indexOf('操作失败') !== -1 || response.indexOf('错误') !== -1 || response.indexOf('失败') !== -1) {
                                CommonUtils.showToast('操作失败', 'error');
                                if (typeof errorCallback === 'function') {
                                    errorCallback(response);
                                }
                            } else {
                                // 默认成功处理
                                CommonUtils.showToast('操作成功', 'success');
                                if (typeof successCallback === 'function') {
                                    successCallback(response);
                                }
                            }
                        } else {
                            CommonUtils.showToast('操作成功', 'success');
                            if (typeof successCallback === 'function') {
                                successCallback(response);
                            }
                        }
                    }
                },
                error: function (xhr, status, error) {
                    $submitBtn.prop('disabled', false).html(originalText);
                    CommonUtils.showToast('网络错误，请重试', 'error');
                    if (typeof errorCallback === 'function') {
                        errorCallback({ error: error, status: status });
                    }
                }
            });

            return false;
        },

        // 页面加载动画
        showLoading: function (message) {
            message = message || '加载中...';
            var loadingHtml =
                '<div id="page-loading" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(255,255,255,0.8); z-index: 9999;">' +
                '<div class="text-center">' +
                '<div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;" role="status"></div>' +
                '<div class="fs-5 text-muted">' + message + '</div>' +
                '</div>' +
                '</div>';

            $('body').append(loadingHtml);
        },

        // 隐藏加载动画
        hideLoading: function () {
            $('#page-loading').remove();
        }
    };

    // 页面加载完成后的初始化
    $(document).ready(function () {
        // 为所有删除链接添加确认对话框
        $(document).on('click', 'a[href*="Del"], a[href*="delete"]', function (e) {
            e.preventDefault();
            var href = $(this).attr('href');
            CommonUtils.confirmDelete('确定要删除这条记录吗？', function () {
                window.location.href = href;
            });
        });

        // 为表单添加回车提交功能
        $('form').on('keypress', function (e) {
            if (e.which === 13 && !$(e.target).is('textarea')) {
                e.preventDefault();
                $(this).find('button[type="submit"]').click();
            }
        });

        // 自动聚焦第一个输入框
        setTimeout(function () {
            $('form .form-control:visible:first').focus();
        }, 100);
    });

    // 删除记录函数
    function deleteData(url, element) {
        // 获取data-id属性值
        var id = $(element).data('id');
        var deleteUrl = url + '?id=' + id;

        CommonUtils.confirmDelete('确定要删除这条记录吗？此操作不可恢复！', function () {
            $.ajax({
                url: deleteUrl,
                type: 'GET',
                success: function (response) {
                    CommonUtils.showToast('删除成功！', 'success');
                    setTimeout(function () {
                        window.location.reload();
                    }, 1500);
                },
                error: function () {
                    CommonUtils.showToast('删除失败，请重试！', 'error');
                }
            });
        });
    }




    // 全局变量
    window.CommonUtils = {
        // Bootstrap Toast提示函数
        showToast: function (message, type, duration) {
            type = type || 'success';
            duration = duration || 3000;

            var toastClass = '';
            var iconClass = '';
            var title = '';

            switch (type) {
                case 'success':
                    toastClass = 'bg-success';
                    iconClass = 'bi bi-check-circle-fill';
                    title = '成功';
                    break;
                case 'error':
                case 'danger':
                    toastClass = 'bg-danger';
                    iconClass = 'bi bi-exclamation-circle-fill';
                    title = '错误';
                    break;
                case 'warning':
                    toastClass = 'bg-warning';
                    iconClass = 'bi bi-exclamation-triangle-fill';
                    title = '警告';
                    break;
                case 'info':
                    toastClass = 'bg-info';
                    iconClass = 'bi bi-info-circle-fill';
                    title = '提示';
                    break;
                default:
                    toastClass = 'bg-primary';
                    iconClass = 'bi bi-info-circle-fill';
                    title = '提示';
            }

            var toastHtml =
                '<div class="toast align-items-center text-white ' + toastClass + ' border-0" role="alert" aria-live="assertive" aria-atomic="true">' +
                '<div class="d-flex">' +
                '<div class="toast-body">' +
                '<i class="' + iconClass + ' me-2"></i>' + message +
                '</div>' +
                '<button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>' +
                '</div>' +
                '</div>';

            var toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                document.body.appendChild(toastContainer);
            }

            toastContainer.innerHTML = toastHtml;
            var toastElement = toastContainer.querySelector('.toast');
            var toast = new bootstrap.Toast(toastElement, { delay: duration });
            toast.show();
        },

        // 确认删除对话框
        confirmDelete: function (message, callback) {
            message = message || '确定要删除这条记录吗？此操作不可恢复！';

            // 创建Bootstrap Modal
            var modalId = 'deleteModal-' + Date.now();
            var modalHtml =
                '<div class="modal fade" id="' + modalId + '" tabindex="-1" aria-hidden="true">' +
                '<div class="modal-dialog modal-dialog-centered">' +
                '<div class="modal-content">' +
                '<div class="modal-header bg-danger text-white">' +
                '<h5 class="modal-title" style="font-weight: normal; font-size: 16px;">' +
                '<i class="bi bi-exclamation-triangle-fill me-2"></i>确认删除' +
                '</h5>' +
                '<button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>' +
                '</div>' +
                '<div class="modal-body">' +
                '<div class="text-center">' +
                '<i class="bi bi-trash3 text-danger" style="font-size: 3rem; margin-bottom: 1rem;"></i>' +
                '<p style="font-size: 14px; font-weight: normal;">' + message + '</p>' +
                '</div>' +
                '</div>' +
                '<div class="modal-footer">' +
                '<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">' +
                '<i class="bi bi-x-lg me-1"></i>取消' +
                '</button>' +
                '<button type="button" class="btn btn-danger" id="confirmDeleteBtn">' +
                '<i class="bi bi-trash3 me-1"></i>确认删除' +
                '</button>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>';

            // 添加modal到页面
            $('body').append(modalHtml);
            var modal = new bootstrap.Modal(document.getElementById(modalId));

            // 绑定确认按钮事件
            $('#' + modalId + ' #confirmDeleteBtn').on('click', function () {
                modal.hide();
                if (typeof callback === 'function') {
                    callback();
                }
            });

            // 显示modal
            modal.show();

            // modal关闭后移除DOM元素
            $('#' + modalId).on('hidden.bs.modal', function () {
                $(this).remove();
            });
        },

        // 表单验证函数
        validateForm: function (formSelector) {
            var isValid = true;
            var $form = $(formSelector);

            // 清除之前的验证状态
            $form.find('.form-control, .form-select').removeClass('is-invalid');
            $form.find('.invalid-feedback').remove();

            // 验证必填字段
            $form.find('[required]').each(function () {
                var $field = $(this);
                var value = $field.val();
                var fieldName = $field.closest('.form-group').find('label').text().replace('*', '').trim();

                if (!value || value.trim() === '') {
                    $field.addClass('is-invalid');
                    $field.after('<div class="invalid-feedback">请输入' + fieldName + '</div>');
                    isValid = false;
                }
            });

            // 验证邮箱格式
            $form.find('input[type="email"]').each(function () {
                var $field = $(this);
                var value = $field.val();

                if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                    $field.addClass('is-invalid');
                    $field.after('<div class="invalid-feedback">请输入正确的邮箱格式</div>');
                    isValid = false;
                }
            });

            // 验证数字格式
            $form.find('input[type="number"]').each(function () {
                var $field = $(this);
                var value = $field.val();

                if (value && isNaN(value)) {
                    $field.addClass('is-invalid');
                    $field.after('<div class="invalid-feedback">请输入有效的数字</div>');
                    isValid = false;
                }
            });

            if (!isValid) {
                CommonUtils.showToast('请检查表单输入', 'error');
                // 滚动到第一个错误字段
                var firstError = $form.find('.is-invalid').first();
                if (firstError.length) {
                    $('html, body').animate({
                        scrollTop: firstError.offset().top - 100
                    }, 500);
                }
            }

            return isValid;
        },

        // AJAX表单提交
        submitForm: function (formSelector, url, successCallback, errorCallback) {
            var $form = $(formSelector);
            var $submitBtn = $form.find('button[type="submit"]');

            // 表单验证
            if (!CommonUtils.validateForm(formSelector)) {
                return false;
            }

            // 显示加载状态
            var originalText = $submitBtn.html();
            $submitBtn.prop('disabled', true).html(
                '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>提交中...'
            );

            // 提交表单
            $.ajax({
                url: url,
                type: 'POST',
                data: $form.serialize(),
                success: function (response) {
                    $submitBtn.prop('disabled', false).html(originalText);

                    // 检查响应是否为JSON格式
                    try {
                        var jsonResponse = typeof response === 'string' ? JSON.parse(response) : response;

                        // 根据Response类的code值处理不同情况
                        if (jsonResponse.code === 200) {
                            // 成功情况 (Response.success())
                            CommonUtils.showToast(jsonResponse.msg || '操作成功', 'success');
                            if (typeof successCallback === 'function') {
                                successCallback(jsonResponse);
                            }
                        } else if (jsonResponse.code === 201) {
                            // 业务逻辑错误 (如账号已存在)
                            CommonUtils.showToast(jsonResponse.msg || '操作失败', 'warning');
                            if (typeof errorCallback === 'function') {
                                errorCallback(jsonResponse);
                            }
                        } else if (jsonResponse.code === 500) {
                            // 服务器错误 (Response.error())
                            CommonUtils.showToast(jsonResponse.msg || '服务器错误', 'error');
                            if (typeof errorCallback === 'function') {
                                errorCallback(jsonResponse);
                            }
                        } else if (jsonResponse.code && jsonResponse.code !== 200) {
                            // 其他错误状态码
                            CommonUtils.showToast(jsonResponse.msg || '操作失败', 'error');
                            if (typeof errorCallback === 'function') {
                                errorCallback(jsonResponse);
                            }
                        } else {
                            // 兼容旧格式 - 检查success字段
                            if (jsonResponse.success || jsonResponse.success === true) {
                                CommonUtils.showToast(jsonResponse.msg || jsonResponse.message || '操作成功', 'success');
                                if (typeof successCallback === 'function') {
                                    successCallback(jsonResponse);
                                }
                            } else {
                                CommonUtils.showToast(jsonResponse.msg || jsonResponse.message || '操作失败', 'error');
                                if (typeof errorCallback === 'function') {
                                    errorCallback(jsonResponse);
                                }
                            }
                        }
                    } catch (e) {
                        // 如果不是JSON格式，检查响应内容
                        if (typeof response === 'string') {
                            if (response.indexOf('操作失败') !== -1 || response.indexOf('错误') !== -1 || response.indexOf('失败') !== -1) {
                                CommonUtils.showToast('操作失败', 'error');
                                if (typeof errorCallback === 'function') {
                                    errorCallback(response);
                                }
                            } else {
                                // 默认成功处理
                                CommonUtils.showToast('操作成功', 'success');
                                if (typeof successCallback === 'function') {
                                    successCallback(response);
                                }
                            }
                        } else {
                            CommonUtils.showToast('操作成功', 'success');
                            if (typeof successCallback === 'function') {
                                successCallback(response);
                            }
                        }
                    }
                },
                error: function (xhr, status, error) {
                    $submitBtn.prop('disabled', false).html(originalText);
                    CommonUtils.showToast('网络错误，请重试', 'error');
                    if (typeof errorCallback === 'function') {
                        errorCallback({ error: error, status: status });
                    }
                }
            });

            return false;
        },

        // 页面加载动画
        showLoading: function (message) {
            message = message || '加载中...';
            var loadingHtml =
                '<div id="page-loading" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(255,255,255,0.8); z-index: 9999;">' +
                '<div class="text-center">' +
                '<div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;" role="status"></div>' +
                '<div class="fs-5 text-muted">' + message + '</div>' +
                '</div>' +
                '</div>';

            $('body').append(loadingHtml);
        },

        // 隐藏加载动画
        hideLoading: function () {
            $('#page-loading').remove();
        }
    };
</script>